'use client';

import { motion } from 'framer-motion';
import { Calendar, Clock, User, ArrowR<PERSON>, Star } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS, BLOG_CATEGORIES } from '@/lib/constants';
import type { BlogPost } from '@/types';

interface BlogCardProps {
  post: BlogPost;
  index?: number;
  variant?: 'default' | 'featured' | 'compact';
}

export function BlogCard({ post, index = 0, variant = 'default' }: BlogCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'research':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'technology':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'career':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'tutorials':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (variant === 'compact') {
    return (
      <motion.div
        variants={ANIMATION_VARIANTS.slideUp}
        whileHover={{ x: 5 }}
        transition={{ duration: 0.2 }}
      >
        <Link href={`/blog/${post.slug}`} className="group">
          <div className="flex items-center space-x-4 p-4 rounded-lg hover:bg-muted/50 transition-colors">
            {post.image && (
              <div className="relative w-16 h-16 flex-shrink-0 rounded-lg overflow-hidden">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover"
                />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <Typography variant="h4" className="text-sm font-medium group-hover:text-primary transition-colors line-clamp-2">
                {post.title}
              </Typography>
              <div className="flex items-center space-x-2 mt-1 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(post.publishedAt)}</span>
                <Clock className="h-3 w-3" />
                <span>{post.readingTime} min read</span>
              </div>
            </div>
          </div>
        </Link>
      </motion.div>
    );
  }

  if (variant === 'featured') {
    return (
      <motion.div
        variants={ANIMATION_VARIANTS.slideUp}
        whileHover={{ y: -8 }}
        transition={{ duration: 0.3 }}
        className="col-span-full"
      >
        <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 group">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            {/* Image */}
            {post.image && (
              <div className="relative h-64 lg:h-full">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>
            )}

            {/* Content */}
            <div className="p-8 flex flex-col justify-center">
              <div className="space-y-4">
                {/* Badges */}
                <div className="flex items-center space-x-2">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                      post.category
                    )}`}
                  >
                    {BLOG_CATEGORIES[post.category as keyof typeof BLOG_CATEGORIES]}
                  </span>
                  {post.featured && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                      <Star className="w-3 h-3 mr-1" />
                      Featured
                    </span>
                  )}
                </div>

                {/* Title and excerpt */}
                <div className="space-y-2">
                  <Typography variant="h2" className="text-2xl lg:text-3xl font-bold group-hover:text-primary transition-colors">
                    {post.title}
                  </Typography>
                  <Typography variant="p" className="text-muted-foreground line-clamp-3">
                    {post.excerpt}
                  </Typography>
                </div>

                {/* Meta information */}
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(post.publishedAt)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{post.readingTime} min read</span>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {post.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground"
                    >
                      {tag}
                    </span>
                  ))}
                  {post.tags.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground">
                      +{post.tags.length - 3} more
                    </span>
                  )}
                </div>

                {/* Read more button */}
                <Button href={`/blog/${post.slug}`} className="group/btn w-fit">
                  Read Full Article
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  }

  // Default variant
  return (
    <motion.div
      variants={ANIMATION_VARIANTS.slideUp}
      whileHover={{ y: -8 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 group overflow-hidden">
        {/* Image */}
        {post.image && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={post.image}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            
            {/* Featured badge */}
            {post.featured && (
              <div className="absolute top-4 right-4">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </span>
              </div>
            )}
          </div>
        )}

        <CardHeader className="space-y-4">
          {/* Category badge */}
          <div className="flex items-center justify-between">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                post.category
              )}`}
            >
              {BLOG_CATEGORIES[post.category as keyof typeof BLOG_CATEGORIES]}
            </span>
          </div>

          {/* Title and excerpt */}
          <div className="space-y-2">
            <CardTitle className="text-xl group-hover:text-primary transition-colors leading-tight line-clamp-2">
              {post.title}
            </CardTitle>
            <CardDescription className="text-sm line-clamp-3">
              {post.excerpt}
            </CardDescription>
          </div>

          {/* Meta information */}
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{post.readingTime} min read</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            {post.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground hover:bg-primary hover:text-primary-foreground transition-colors"
              >
                {tag}
              </span>
            ))}
            {post.tags.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground">
                +{post.tags.length - 3} more
              </span>
            )}
          </div>

          {/* Read more button */}
          <Button href={`/blog/${post.slug}`} variant="ghost" className="w-full group/btn">
            Read More
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
