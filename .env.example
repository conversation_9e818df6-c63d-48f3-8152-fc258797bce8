# Site Configuration
NEXT_PUBLIC_SITE_URL=https://premkatuwal.com
NEXT_PUBLIC_SITE_NAME="Prem <PERSON>uwal - AI Researcher & Software Engineer"
NEXT_PUBLIC_SITE_DESCRIPTION="Master's student in Computer Science (AI) at UESTC, ranked 3rd globally in AI"

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_GTAG_ID=GT-XXXXXXXXX
NEXT_PUBLIC_GOOGLE_ANALYTICS_4_ID=G-XXXXXXXXXX

# Social Media
NEXT_PUBLIC_TWITTER_HANDLE=@premkatuwal
NEXT_PUBLIC_LINKEDIN_URL=https://linkedin.com/in/premkatuwal
NEXT_PUBLIC_GITHUB_URL=https://github.com/Katwal-77

# Contact Form
CONTACT_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=prem-app-password

# Database (if needed for future features)
DATABASE_URL=postgresql://username:password@localhost:5432/portfolio

# API Keys
OPENAI_API_KEY=sk-xxxxxxxxxx
RESEND_API_KEY=re_xxxxxxxxxx

# Security
NEXTAUTH_SECRET=prem-nextauth-secret
NEXTAUTH_URL=https://premkatuwal.com

# Monitoring
SENTRY_DSN=https://<EMAIL>/xxxxxxxxxx
UPTIME_ROBOT_API_KEY=ur-xxxxxxxxxx

# Performance Monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx
NEXT_PUBLIC_SPEED_INSIGHTS=true

# Feature Flags
NEXT_PUBLIC_ENABLE_BLOG=true
NEXT_PUBLIC_ENABLE_TESTIMONIALS=true
NEXT_PUBLIC_ENABLE_JOURNEY=true
NEXT_PUBLIC_ENABLE_CONTACT_FORM=true

# Development
NODE_ENV=production
NEXT_PUBLIC_VERCEL_ENV=production
