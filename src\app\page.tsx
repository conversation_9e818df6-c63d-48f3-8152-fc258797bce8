import { Hero } from '@/components/sections/Hero';
import { FeaturedWork } from '@/components/sections/FeaturedWork';
import { Stats } from '@/components/sections/Stats';
import personalData from '@/data/personal.json';
import featuredWorkData from '@/data/featured-work.json';
import statsData from '@/data/stats.json';

export default function Home() {
  return (
    <div className="space-y-0">
      {/* Hero Section */}
      <Hero
        name={personalData.name}
        title={personalData.title}
        elevatorPitch={personalData.elevator_pitch}
        profileImage={personalData.profile_image}
        resumeUrl={personalData.resume_url}
        social={{
          github: personalData.social.github,
          linkedin: personalData.social.linkedin,
          email: personalData.email,
        }}
      />

      {/* Stats Section */}
      <Stats stats={statsData} />

      {/* Featured Work Section */}
      <FeaturedWork items={featuredWorkData.slice(0, 6)} />
    </div>
  );
}
