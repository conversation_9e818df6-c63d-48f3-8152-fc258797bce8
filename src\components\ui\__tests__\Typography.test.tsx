import { render, screen } from '@/__tests__/utils/test-utils'
import { Typography } from '../Typography'

describe('Typography Component', () => {
  it('renders with default variant (p)', () => {
    render(<Typography>Test content</Typography>)
    const element = screen.getByText('Test content')
    expect(element).toBeInTheDocument()
    expect(element.tagName).toBe('P')
  })

  it('renders with h1 variant', () => {
    render(<Typography variant="h1">Heading 1</Typography>)
    const element = screen.getByText('Heading 1')
    expect(element.tagName).toBe('H1')
    expect(element).toHaveClass('scroll-m-20', 'text-4xl', 'font-extrabold', 'tracking-tight', 'lg:text-5xl')
  })

  it('renders with h2 variant', () => {
    render(<Typography variant="h2">Heading 2</Typography>)
    const element = screen.getByText('Heading 2')
    expect(element.tagName).toBe('H2')
    expect(element).toHaveClass('scroll-m-20', 'border-b', 'pb-2', 'text-3xl', 'font-semibold', 'tracking-tight', 'first:mt-0')
  })

  it('renders with lead variant', () => {
    render(<Typography variant="lead">Lead text</Typography>)
    const element = screen.getByText('Lead text')
    expect(element.tagName).toBe('P')
    expect(element).toHaveClass('text-xl', 'text-muted-foreground')
  })

  it('renders with small variant', () => {
    render(<Typography variant="small">Small text</Typography>)
    const element = screen.getByText('Small text')
    expect(element.tagName).toBe('SMALL')
    expect(element).toHaveClass('text-sm', 'font-medium', 'leading-none')
  })

  it('applies custom className', () => {
    render(<Typography className="custom-class">Test</Typography>)
    const element = screen.getByText('Test')
    expect(element).toHaveClass('custom-class')
  })

  it('renders as link when href is provided', () => {
    render(<Typography href="/test">Link text</Typography>)
    const element = screen.getByText('Link text')
    expect(element.tagName).toBe('A')
    expect(element).toHaveAttribute('href', '/test')
  })

  it('renders external link with proper attributes', () => {
    render(<Typography href="https://example.com" external>External link</Typography>)
    const element = screen.getByText('External link')
    expect(element).toHaveAttribute('href', 'https://example.com')
    expect(element).toHaveAttribute('target', '_blank')
    expect(element).toHaveAttribute('rel', 'noopener noreferrer')
  })

  it('forwards ref correctly', () => {
    const ref = { current: null }
    render(<Typography ref={ref}>Test</Typography>)
    expect(ref.current).toBeInstanceOf(HTMLParagraphElement)
  })

  it('handles all heading variants correctly', () => {
    const headings = ['h1', 'h2', 'h3', 'h4'] as const
    
    headings.forEach(variant => {
      render(<Typography variant={variant}>{variant} text</Typography>)
      const element = screen.getByText(`${variant} text`)
      expect(element.tagName).toBe(variant.toUpperCase())
    })
  })

  it('applies correct styles for muted variant', () => {
    render(<Typography variant="muted">Muted text</Typography>)
    const element = screen.getByText('Muted text')
    expect(element).toHaveClass('text-sm', 'text-muted-foreground')
  })

  it('handles blockquote variant', () => {
    render(<Typography variant="blockquote">Quote text</Typography>)
    const element = screen.getByText('Quote text')
    expect(element.tagName).toBe('BLOCKQUOTE')
    expect(element).toHaveClass('mt-6', 'border-l-2', 'pl-6', 'italic')
  })

  it('handles list variant', () => {
    render(<Typography variant="list">List item</Typography>)
    const element = screen.getByText('List item')
    expect(element.tagName).toBe('UL')
    expect(element).toHaveClass('my-6', 'ml-6', 'list-disc')
  })

  it('handles code variant', () => {
    render(<Typography variant="code">Code text</Typography>)
    const element = screen.getByText('Code text')
    expect(element.tagName).toBe('CODE')
    expect(element).toHaveClass('relative', 'rounded', 'bg-muted', 'px-[0.3rem]', 'py-[0.2rem]', 'font-mono', 'text-sm', 'font-semibold')
  })
})
