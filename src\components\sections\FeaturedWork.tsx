'use client';

import { motion } from 'framer-motion';
import { ArrowR<PERSON>, ExternalLink, Github } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';

interface FeaturedItem {
  id: string;
  title: string;
  description: string;
  category: 'research' | 'project' | 'publication';
  tags: string[];
  link?: string;
  githubUrl?: string;
  featured?: boolean;
}

interface FeaturedWorkProps {
  items: FeaturedItem[];
}

export function FeaturedWork({ items }: FeaturedWorkProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'research':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'project':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'publication':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'research':
        return '🔬';
      case 'project':
        return '💻';
      case 'publication':
        return '📄';
      default:
        return '📋';
    }
  };

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="space-y-12"
        >
          {/* Section Header */}
          <motion.div variants={ANIMATION_VARIANTS.slideUp} className="text-center space-y-4">
            <Typography variant="h2" className="text-3xl lg:text-4xl font-bold">
              Featured Work
            </Typography>
            <Typography variant="lead" className="max-w-2xl mx-auto">
              A selection of my most impactful research, projects, and publications
              that demonstrate my expertise and contributions to the field.
            </Typography>
          </motion.div>

          {/* Featured Items Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {items.map((item, index) => (
              <motion.div
                key={item.id}
                variants={ANIMATION_VARIANTS.slideUp}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 group">
                  <CardHeader className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                          item.category
                        )}`}
                      >
                        <span className="mr-1">{getCategoryIcon(item.category)}</span>
                        {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                      </span>
                      {item.featured && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                          ⭐ Featured
                        </span>
                      )}
                    </div>
                    <CardTitle className="group-hover:text-primary transition-colors">
                      {item.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-3">
                      {item.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {item.tags.slice(0, 3).map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-secondary text-secondary-foreground"
                        >
                          {tag}
                        </span>
                      ))}
                      {item.tags.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-secondary text-secondary-foreground">
                          +{item.tags.length - 3} more
                        </span>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2 pt-2">
                      {item.link && (
                        <Button
                          href={item.link}
                          variant="ghost"
                          size="sm"
                          external
                          className="flex-1 group/btn"
                        >
                          View Details
                          <ExternalLink className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-0.5" />
                        </Button>
                      )}
                      {item.githubUrl && (
                        <Button
                          href={item.githubUrl}
                          variant="ghost"
                          size="icon"
                          external
                          aria-label="View on GitHub"
                        >
                          <Github className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* View All Button */}
          <motion.div variants={ANIMATION_VARIANTS.slideUp} className="text-center pt-8">
            <Button href="/projects" variant="outline" size="lg" className="group">
              View All Work
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
