'use client';

import { useState } from 'react';
import { Search, Filter, X, SortAsc, SortDesc } from 'lucide-react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';
import type { SearchFilters } from '@/types';

interface SearchAndFilterProps {
  onFiltersChange: (filters: SearchFilters) => void;
  totalResults: number;
  availableTags: string[];
  availableYears: number[];
  availableTypes: string[];
}

export function SearchAndFilter({
  onFiltersChange,
  totalResults,
  availableTags,
  availableYears,
  availableTypes,
}: SearchAndFilterProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    tags: [],
    year: undefined,
    type: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'year' | 'citations' | 'title'>('year');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange({ ...updatedFilters, sortBy, sortOrder });
  };

  const clearFilters = () => {
    const clearedFilters: SearchFilters = {
      query: '',
      tags: [],
      year: undefined,
      type: '',
    };
    setFilters(clearedFilters);
    onFiltersChange({ ...clearedFilters, sortBy, sortOrder });
  };

  const toggleTag = (tag: string) => {
    const newTags = filters.tags?.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...(filters.tags || []), tag];
    updateFilters({ tags: newTags });
  };

  const toggleSort = () => {
    const newOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    setSortOrder(newOrder);
    onFiltersChange({ ...filters, sortBy, sortOrder: newOrder });
  };

  const changeSortBy = (newSortBy: 'year' | 'citations' | 'title') => {
    setSortBy(newSortBy);
    onFiltersChange({ ...filters, sortBy: newSortBy, sortOrder });
  };

  const hasActiveFilters = filters.query || filters.tags?.length || filters.year || filters.type;

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search publications by title, authors, or keywords..."
          value={filters.query || ''}
          onChange={(e) => updateFilters({ query: e.target.value })}
          className="pl-10 pr-4"
        />
      </div>

      {/* Filter Controls */}
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 text-xs flex items-center justify-center">
                {(filters.tags?.length || 0) + (filters.year ? 1 : 0) + (filters.type ? 1 : 0)}
              </span>
            )}
          </Button>

          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Typography variant="small" className="text-muted-foreground">
            {totalResults} publications
          </Typography>
          
          <div className="flex items-center space-x-1">
            <select
              value={sortBy}
              onChange={(e) => changeSortBy(e.target.value as 'year' | 'citations' | 'title')}
              className="text-sm border border-input rounded px-2 py-1 bg-background"
            >
              <option value="year">Year</option>
              <option value="citations">Citations</option>
              <option value="title">Title</option>
            </select>
            <Button variant="ghost" size="sm" onClick={toggleSort}>
              {sortOrder === 'desc' ? <SortDesc className="h-4 w-4" /> : <SortAsc className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <Card>
          <CardContent className="p-4 space-y-4">
            {/* Publication Type Filter */}
            <div className="space-y-2">
              <Typography variant="small" className="font-medium">Publication Type</Typography>
              <div className="flex flex-wrap gap-2">
                {availableTypes.map((type) => (
                  <Button
                    key={type}
                    variant={filters.type === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateFilters({ type: filters.type === type ? '' : type })}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Button>
                ))}
              </div>
            </div>

            {/* Year Filter */}
            <div className="space-y-2">
              <Typography variant="small" className="font-medium">Year</Typography>
              <div className="flex flex-wrap gap-2">
                {availableYears.sort((a, b) => b - a).map((year) => (
                  <Button
                    key={year}
                    variant={filters.year === year ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateFilters({ year: filters.year === year ? undefined : year })}
                  >
                    {year}
                  </Button>
                ))}
              </div>
            </div>

            {/* Tags Filter */}
            <div className="space-y-2">
              <Typography variant="small" className="font-medium">Research Areas</Typography>
              <div className="flex flex-wrap gap-2">
                {availableTags.slice(0, 15).map((tag) => (
                  <Button
                    key={tag}
                    variant={filters.tags?.includes(tag) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleTag(tag)}
                  >
                    {tag}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
