import { render, screen, fireEvent } from '@/__tests__/utils/test-utils'
import { Button } from '../Button'

describe('Button Component', () => {
  it('renders with default variant and size', () => {
    render(<Button>Click me</Button>)
    const button = screen.getByRole('button', { name: 'Click me' })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-primary', 'text-primary-foreground', 'hover:bg-primary/90')
  })

  it('renders with different variants', () => {
    const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'] as const
    
    variants.forEach(variant => {
      render(<Button variant={variant}>{variant} button</Button>)
      const button = screen.getByRole('button', { name: `${variant} button` })
      expect(button).toBeInTheDocument()
    })
  })

  it('renders with different sizes', () => {
    const sizes = ['default', 'sm', 'lg', 'icon'] as const
    
    sizes.forEach(size => {
      render(<Button size={size}>{size} button</Button>)
      const button = screen.getByRole('button', { name: `${size} button` })
      expect(button).toBeInTheDocument()
    })
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('renders as disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled button</Button>)
    const button = screen.getByRole('button', { name: 'Disabled button' })
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:pointer-events-none', 'disabled:opacity-50')
  })

  it('renders as link when href is provided', () => {
    render(<Button href="/test">Link button</Button>)
    const link = screen.getByRole('link', { name: 'Link button' })
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/test')
  })

  it('renders external link with proper attributes', () => {
    render(<Button href="https://example.com" external>External link</Button>)
    const link = screen.getByRole('link', { name: 'External link' })
    expect(link).toHaveAttribute('href', 'https://example.com')
    expect(link).toHaveAttribute('target', '_blank')
    expect(link).toHaveAttribute('rel', 'noopener noreferrer')
  })

  it('applies custom className', () => {
    render(<Button className="custom-class">Test</Button>)
    const button = screen.getByRole('button', { name: 'Test' })
    expect(button).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = { current: null }
    render(<Button ref={ref}>Test</Button>)
    expect(ref.current).toBeInstanceOf(HTMLButtonElement)
  })

  it('handles outline variant correctly', () => {
    render(<Button variant="outline">Outline button</Button>)
    const button = screen.getByRole('button', { name: 'Outline button' })
    expect(button).toHaveClass('border', 'border-input', 'bg-background', 'hover:bg-accent')
  })

  it('handles ghost variant correctly', () => {
    render(<Button variant="ghost">Ghost button</Button>)
    const button = screen.getByRole('button', { name: 'Ghost button' })
    expect(button).toHaveClass('hover:bg-accent', 'hover:text-accent-foreground')
  })

  it('handles link variant correctly', () => {
    render(<Button variant="link">Link button</Button>)
    const button = screen.getByRole('button', { name: 'Link button' })
    expect(button).toHaveClass('text-primary', 'underline-offset-4', 'hover:underline')
  })

  it('handles icon size correctly', () => {
    render(<Button size="icon">🔍</Button>)
    const button = screen.getByRole('button')
    expect(button).toHaveClass('h-10', 'w-10')
  })

  it('prevents click when disabled', () => {
    const handleClick = jest.fn()
    render(<Button disabled onClick={handleClick}>Disabled</Button>)
    
    const button = screen.getByRole('button', { name: 'Disabled' })
    fireEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('supports asChild prop for composition', () => {
    render(
      <Button asChild>
        <span>Custom element</span>
      </Button>
    )
    const element = screen.getByText('Custom element')
    expect(element.tagName).toBe('SPAN')
  })
})
