'use client';

import { motion } from 'framer-motion';
import { Download, MapPin, Calendar, GraduationCap } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';

interface AboutHeroProps {
  name: string;
  title: string;
  bio: string;
  location: string;
  profileImage?: string;
  resumeUrl?: string;
  currentStatus?: string;
  education?: string;
}

export function AboutHero({
  name,
  title,
  bio,
  location,
  profileImage,
  resumeUrl,
  currentStatus = "Mphil/Masters Student",
  education = "Computer Science AI"
}: AboutHeroProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const quickFacts = [
    {
      icon: GraduationCap,
      label: "Current Status",
      value: currentStatus,
    },
    {
      icon: Calendar,
      label: "Field of Study",
      value: education,
    },
    {
      icon: MapPin,
      label: "Location",
      value: location,
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-background via-muted/30 to-background">
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
        >
          {/* Profile Image */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideRight}
            className="flex justify-center lg:justify-start order-2 lg:order-1"
          >
            <div className="relative">
              <motion.div
                className="w-80 h-80 lg:w-96 lg:h-96 rounded-2xl overflow-hidden shadow-2xl"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                {profileImage ? (
                  <img
                    src={profileImage}
                    alt={`${name} - Profile`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                    <Typography variant="h1" className="text-6xl text-muted-foreground">
                      {name.split(' ').map(n => n[0]).join('')}
                    </Typography>
                  </div>
                )}
              </motion.div>

              {/* Floating Badge */}
              <motion.div
                className="absolute -bottom-4 -right-4 bg-primary text-primary-foreground px-4 py-2 rounded-full shadow-lg"
                animate={{
                  y: [-5, 5, -5],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                <Typography variant="small" className="font-medium">
                  Available for opportunities
                </Typography>
              </motion.div>
            </div>
          </motion.div>

          {/* Content */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideLeft}
            className="space-y-8 order-1 lg:order-2"
          >
            {/* Header */}
            <div className="space-y-4">
              <Typography variant="large" className="text-primary font-medium">
                About Me
              </Typography>
              <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
                {name}
              </Typography>
              <Typography variant="h3" className="text-xl lg:text-2xl text-muted-foreground font-normal">
                {title}
              </Typography>
            </div>

            {/* Bio */}
            <Typography variant="lead" className="text-lg leading-relaxed">
              {bio}
            </Typography>

            {/* Quick Facts */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {quickFacts.map((fact, index) => (
                <motion.div
                  key={fact.label}
                  variants={ANIMATION_VARIANTS.slideUp}
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="text-center p-4 hover:shadow-md transition-all duration-300">
                    <CardContent className="space-y-2 p-0">
                      <fact.icon className="h-6 w-6 mx-auto text-primary" />
                      <Typography variant="small" className="text-muted-foreground">
                        {fact.label}
                      </Typography>
                      <Typography variant="small" className="font-medium">
                        {fact.value}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              {resumeUrl && (
                <Button href={resumeUrl} size="lg" external className="group">
                  <Download className="mr-2 h-4 w-4 transition-transform group-hover:translate-y-0.5" />
                  Download Resume
                </Button>
              )}
              <Button href="/contact" variant="outline" size="lg">
                Get in Touch
              </Button>
            </div>

            {/* Additional Info */}
            <div className="p-6 bg-muted/50 rounded-lg border">
              <Typography variant="p" className="text-muted-foreground">
                <strong>Fun fact:</strong> When I'm not diving deep into research or coding, 
                you'll find me volunteering in my community, exploring new technologies, 
                or sharing knowledge through writing and mentoring.
              </Typography>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
