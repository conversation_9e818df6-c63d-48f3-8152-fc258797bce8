import { render, screen, fireEvent } from '@/__tests__/utils/test-utils'
import { ProjectCard } from '../ProjectCard'
import { mockProject } from '@/__tests__/utils/test-utils'

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />,
}))

describe('ProjectCard Component', () => {
  const defaultProps = {
    project: mockProject,
    index: 0,
  }

  it('renders project information correctly', () => {
    render(<ProjectCard {...defaultProps} />)
    
    expect(screen.getByText(mockProject.title)).toBeInTheDocument()
    expect(screen.getByText(mockProject.description)).toBeInTheDocument()
    expect(screen.getByAltText(mockProject.title)).toBeInTheDocument()
  })

  it('displays technologies as tags', () => {
    render(<ProjectCard {...defaultProps} />)
    
    mockProject.technologies.forEach(tech => {
      expect(screen.getByText(tech)).toBeInTheDocument()
    })
  })

  it('shows featured badge when project is featured', () => {
    render(<ProjectCard {...defaultProps} />)
    expect(screen.getByText('Featured')).toBeInTheDocument()
  })

  it('does not show featured badge when project is not featured', () => {
    const nonFeaturedProject = { ...mockProject, featured: false }
    render(<ProjectCard project={nonFeaturedProject} />)
    expect(screen.queryByText('Featured')).not.toBeInTheDocument()
  })

  it('displays correct status badge', () => {
    render(<ProjectCard {...defaultProps} />)
    expect(screen.getByText('Completed')).toBeInTheDocument()
  })

  it('renders demo link when demoUrl is provided', () => {
    render(<ProjectCard {...defaultProps} />)
    const demoLink = screen.getByRole('link', { name: /live demo/i })
    expect(demoLink).toHaveAttribute('href', mockProject.demoUrl)
    expect(demoLink).toHaveAttribute('target', '_blank')
  })

  it('renders GitHub link when githubUrl is provided', () => {
    render(<ProjectCard {...defaultProps} />)
    const githubLink = screen.getByRole('link', { name: /view code/i })
    expect(githubLink).toHaveAttribute('href', mockProject.githubUrl)
    expect(githubLink).toHaveAttribute('target', '_blank')
  })

  it('does not render demo link when demoUrl is not provided', () => {
    const projectWithoutDemo = { ...mockProject, demoUrl: undefined }
    render(<ProjectCard project={projectWithoutDemo} />)
    expect(screen.queryByRole('link', { name: /live demo/i })).not.toBeInTheDocument()
  })

  it('does not render GitHub link when githubUrl is not provided', () => {
    const projectWithoutGithub = { ...mockProject, githubUrl: undefined }
    render(<ProjectCard project={projectWithoutGithub} />)
    expect(screen.queryByRole('link', { name: /view code/i })).not.toBeInTheDocument()
  })

  it('applies correct category styling', () => {
    render(<ProjectCard {...defaultProps} />)
    const categoryBadge = screen.getByText('Web Development')
    expect(categoryBadge).toBeInTheDocument()
  })

  it('handles different project statuses', () => {
    const statuses = ['completed', 'in-progress', 'planned'] as const
    
    statuses.forEach(status => {
      const project = { ...mockProject, status }
      render(<ProjectCard project={project} />)
      
      // Check that status is displayed (exact text may vary based on implementation)
      expect(screen.getByText(new RegExp(status.replace('-', ' '), 'i'))).toBeInTheDocument()
    })
  })

  it('renders compact variant correctly', () => {
    render(<ProjectCard {...defaultProps} variant="compact" />)
    
    // In compact variant, some elements might be hidden or styled differently
    expect(screen.getByText(mockProject.title)).toBeInTheDocument()
    expect(screen.getByText(mockProject.description)).toBeInTheDocument()
  })

  it('renders featured variant correctly', () => {
    render(<ProjectCard {...defaultProps} variant="featured" />)
    
    expect(screen.getByText(mockProject.title)).toBeInTheDocument()
    expect(screen.getByText(mockProject.description)).toBeInTheDocument()
  })

  it('handles missing image gracefully', () => {
    const projectWithoutImage = { ...mockProject, image: undefined }
    render(<ProjectCard project={projectWithoutImage} />)
    
    // Should still render other content
    expect(screen.getByText(mockProject.title)).toBeInTheDocument()
    expect(screen.getByText(mockProject.description)).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<ProjectCard {...defaultProps} />)
    
    const image = screen.getByAltText(mockProject.title)
    expect(image).toBeInTheDocument()
    
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAttribute('href')
    })
  })

  it('handles click events on interactive elements', () => {
    render(<ProjectCard {...defaultProps} />)
    
    const demoLink = screen.getByRole('link', { name: /live demo/i })
    const githubLink = screen.getByRole('link', { name: /view code/i })
    
    // These should be clickable (no need to test actual navigation in unit tests)
    expect(demoLink).toBeInTheDocument()
    expect(githubLink).toBeInTheDocument()
  })

  it('displays all technologies when there are many', () => {
    const projectWithManyTechs = {
      ...mockProject,
      technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'Docker', 'AWS']
    }
    
    render(<ProjectCard project={projectWithManyTechs} />)
    
    projectWithManyTechs.technologies.forEach(tech => {
      expect(screen.getByText(tech)).toBeInTheDocument()
    })
  })

  it('truncates long descriptions appropriately', () => {
    const projectWithLongDescription = {
      ...mockProject,
      description: 'This is a very long description that should be truncated in the UI to maintain a clean layout and prevent the card from becoming too tall and disrupting the grid layout.'
    }
    
    render(<ProjectCard project={projectWithLongDescription} />)
    
    // The description should be present (truncation is handled by CSS)
    expect(screen.getByText(projectWithLongDescription.description)).toBeInTheDocument()
  })
})
