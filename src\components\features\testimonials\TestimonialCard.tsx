'use client';

import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import type { Testimonial } from '@/types';

interface TestimonialCardProps {
  testimonial: Testimonial;
  index?: number;
  variant?: 'default' | 'featured' | 'compact';
}

export function TestimonialCard({ testimonial, index = 0, variant = 'default' }: TestimonialCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'academic':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'professional':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'community':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'research':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'mentoring':
        return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      case 'speaking':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      case 'international':
        return 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200';
      case 'education':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  if (variant === 'compact') {
    return (
      <motion.div
        variants={ANIMATION_VARIANTS.slideUp}
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="h-full hover:shadow-md transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <div className="relative w-12 h-12 flex-shrink-0">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  fill
                  className="rounded-full object-cover"
                />
              </div>
              <div className="flex-1 min-w-0">
                <Typography variant="small" className="font-medium line-clamp-2 mb-1">
                  "{testimonial.content}"
                </Typography>
                <Typography variant="small" className="text-muted-foreground">
                  — {testimonial.name}
                </Typography>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  if (variant === 'featured') {
    return (
      <motion.div
        variants={ANIMATION_VARIANTS.slideUp}
        whileHover={{ y: -8 }}
        transition={{ duration: 0.3 }}
        className="col-span-full"
      >
        <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 group bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
          <CardContent className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
              {/* Quote Content */}
              <div className="lg:col-span-2 space-y-6">
                <div className="flex items-center space-x-2">
                  <Quote className="h-8 w-8 text-primary/60" />
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(
                      testimonial.category
                    )}`}
                  >
                    {testimonial.category.charAt(0).toUpperCase() + testimonial.category.slice(1)}
                  </span>
                </div>

                <Typography variant="lead" className="text-lg leading-relaxed italic">
                  "{testimonial.content}"
                </Typography>

                {/* Rating */}
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < testimonial.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* Author Info */}
              <div className="text-center lg:text-left space-y-4">
                <div className="relative w-24 h-24 mx-auto lg:mx-0">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    fill
                    className="rounded-full object-cover ring-4 ring-primary/20"
                  />
                </div>
                <div>
                  <Typography variant="h4" className="font-semibold">
                    {testimonial.name}
                  </Typography>
                  <Typography variant="small" className="text-primary font-medium">
                    {testimonial.role}
                  </Typography>
                  <Typography variant="small" className="text-muted-foreground">
                    {testimonial.organization}
                  </Typography>
                  <Typography variant="small" className="text-muted-foreground mt-2">
                    {formatDate(testimonial.date)}
                  </Typography>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // Default variant
  return (
    <motion.div
      variants={ANIMATION_VARIANTS.slideUp}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="h-full hover:shadow-lg transition-all duration-300 group">
        <CardContent className="p-6 space-y-4">
          {/* Category Badge */}
          <div className="flex items-center justify-between">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                testimonial.category
              )}`}
            >
              {testimonial.category.charAt(0).toUpperCase() + testimonial.category.slice(1)}
            </span>
            <Quote className="h-6 w-6 text-primary/40" />
          </div>

          {/* Quote Content */}
          <Typography variant="p" className="text-sm leading-relaxed italic line-clamp-4">
            "{testimonial.content}"
          </Typography>

          {/* Rating */}
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${
                  i < testimonial.rating
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>

          {/* Author Info */}
          <div className="flex items-center space-x-3 pt-4 border-t">
            <div className="relative w-12 h-12 flex-shrink-0">
              <Image
                src={testimonial.image}
                alt={testimonial.name}
                fill
                className="rounded-full object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <Typography variant="small" className="font-medium">
                {testimonial.name}
              </Typography>
              <Typography variant="small" className="text-primary text-xs">
                {testimonial.role}
              </Typography>
              <Typography variant="small" className="text-muted-foreground text-xs line-clamp-1">
                {testimonial.organization}
              </Typography>
            </div>
          </div>

          {/* Date */}
          <Typography variant="small" className="text-muted-foreground text-xs text-right">
            {formatDate(testimonial.date)}
          </Typography>
        </CardContent>
      </Card>
    </motion.div>
  );
}
