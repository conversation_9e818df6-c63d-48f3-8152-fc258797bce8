'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Star, Filter, Users, Award, Heart, TrendingUp } from 'lucide-react';
import { Typography } from '@/components/ui/Typography';
import { Button } from '@/components/ui/Button';
import { TestimonialCard } from '@/components/features/testimonials/TestimonialCard';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import type { Testimonial } from '@/types';

interface TestimonialsPageClientProps {
  testimonials: Testimonial[];
}

export function TestimonialsPageClient({ testimonials }: TestimonialsPageClientProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get unique categories
  const categories = useMemo(() => {
    const cats = Array.from(new Set(testimonials.map(t => t.category)));
    return ['all', ...cats];
  }, [testimonials]);

  // Filter testimonials
  const filteredTestimonials = useMemo(() => {
    if (selectedCategory === 'all') return testimonials;
    return testimonials.filter(t => t.category === selectedCategory);
  }, [testimonials, selectedCategory]);

  // Get featured testimonials
  const featuredTestimonials = useMemo(() => {
    return testimonials.filter(t => t.featured);
  }, [testimonials]);

  // Calculate stats
  const stats = useMemo(() => {
    const avgRating = testimonials.reduce((sum, t) => sum + t.rating, 0) / testimonials.length;
    const categoryCounts = testimonials.reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: testimonials.length,
      averageRating: avgRating,
      categories: Object.keys(categoryCounts).length,
      featured: featuredTestimonials.length,
    };
  }, [testimonials, featuredTestimonials]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      all: 'All Testimonials',
      academic: 'Academic',
      professional: 'Professional',
      community: 'Community',
      research: 'Research',
      mentoring: 'Mentoring',
      speaking: 'Speaking',
      international: 'International',
      education: 'Education',
    };
    return labels[category] || category.charAt(0).toUpperCase() + category.slice(1);
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          Testimonials & Recommendations
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          What colleagues, supervisors, mentees, and collaborators say about working with me 
          across academia, industry, and community service.
        </Typography>
      </motion.div>

      {/* Statistics */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
      >
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Users className="h-8 w-8 text-primary" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.total}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Total Testimonials
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Star className="h-8 w-8 text-yellow-500" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.averageRating.toFixed(1)}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Average Rating
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Filter className="h-8 w-8 text-accent" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.categories}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Categories
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Award className="h-8 w-8 text-primary" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.featured}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Featured
          </Typography>
        </div>
      </motion.div>

      {/* Category Filter */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="flex flex-wrap justify-center gap-3"
      >
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className="capitalize"
          >
            {getCategoryLabel(category)}
            {category !== 'all' && (
              <span className="ml-2 text-xs">
                ({testimonials.filter(t => t.category === category).length})
              </span>
            )}
          </Button>
        ))}
      </motion.div>

      {/* Featured Testimonials */}
      {selectedCategory === 'all' && featuredTestimonials.length > 0 && (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          className="space-y-8"
        >
          <Typography variant="h2" className="text-2xl font-bold text-center">
            Featured Testimonials
          </Typography>
          <div className="space-y-8">
            {featuredTestimonials.map((testimonial) => (
              <TestimonialCard
                key={testimonial.id}
                testimonial={testimonial}
                variant="featured"
              />
            ))}
          </div>
        </motion.div>
      )}

      {/* All Testimonials */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-8"
      >
        {selectedCategory === 'all' ? (
          <Typography variant="h2" className="text-2xl font-bold text-center">
            All Testimonials
          </Typography>
        ) : (
          <Typography variant="h2" className="text-2xl font-bold text-center">
            {getCategoryLabel(selectedCategory)} Testimonials
          </Typography>
        )}
        
        {filteredTestimonials.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTestimonials
              .filter(t => selectedCategory === 'all' ? !t.featured : true)
              .map((testimonial, index) => (
                <TestimonialCard
                  key={testimonial.id}
                  testimonial={testimonial}
                  index={index}
                  variant="default"
                />
              ))}
          </div>
        ) : (
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="text-center py-12"
          >
            <Typography variant="h3" className="text-muted-foreground mb-4">
              No testimonials found
            </Typography>
            <Typography variant="p" className="text-muted-foreground">
              Try selecting a different category to see relevant testimonials.
            </Typography>
          </motion.div>
        )}
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8"
      >
        <div className="max-w-2xl mx-auto p-8 bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg border">
          <Typography variant="h3" className="mb-4">
            Want to Work Together?
          </Typography>
          <Typography variant="p" className="text-muted-foreground mb-6">
            These testimonials reflect my commitment to excellence, collaboration, and making a positive impact. 
            If you're looking for someone who brings both technical expertise and genuine care for people and projects, 
            I'd love to discuss how we can work together.
          </Typography>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button href="/contact" size="lg">
              Get in Touch
            </Button>
            <Button href="/projects" variant="outline" size="lg">
              View My Work
            </Button>
          </div>
        </div>
      </motion.div>

      {/* Impact Summary */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8 border-t"
      >
        <Typography variant="h3" className="text-2xl font-bold mb-8">
          Impact Across Communities
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="space-y-3">
            <div className="flex justify-center">
              <TrendingUp className="h-12 w-12 text-primary" />
            </div>
            <Typography variant="h4" className="font-semibold">
              Professional Excellence
            </Typography>
            <Typography variant="p" className="text-muted-foreground text-sm">
              Consistently delivering high-quality work and innovative solutions in both industry and academic settings.
            </Typography>
          </div>
          <div className="space-y-3">
            <div className="flex justify-center">
              <Users className="h-12 w-12 text-accent" />
            </div>
            <Typography variant="h4" className="font-semibold">
              Mentorship & Teaching
            </Typography>
            <Typography variant="p" className="text-muted-foreground text-sm">
              Empowering others through patient teaching, thoughtful mentorship, and knowledge sharing.
            </Typography>
          </div>
          <div className="space-y-3">
            <div className="flex justify-center">
              <Heart className="h-12 w-12 text-red-500" />
            </div>
            <Typography variant="h4" className="font-semibold">
              Community Impact
            </Typography>
            <Typography variant="p" className="text-muted-foreground text-sm">
              Making a meaningful difference through volunteer work, community education, and social initiatives.
            </Typography>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
