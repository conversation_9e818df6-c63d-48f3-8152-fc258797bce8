'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MapPin, Calendar, Heart, Lightbulb, Target, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import type { JourneyStep } from '@/types';

interface JourneyTimelineProps {
  journeySteps: JourneyStep[];
}

export function JourneyTimeline({ journeySteps }: JourneyTimelineProps) {
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [hoveredStep, setHoveredStep] = useState<string | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const pathVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        duration: 3,
        ease: "easeInOut",
      },
    },
  };

  const stepVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15,
      },
    },
    hover: {
      scale: 1.1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
  };

  // Create SVG path points for the journey
  const createPath = () => {
    const points = journeySteps.map(step => `${step.coordinates.x},${step.coordinates.y}`);
    return `M ${points.join(' L ')}`;
  };

  return (
    <div className="space-y-12">
      {/* Interactive Journey Map */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="relative"
      >
        <Card className="overflow-hidden bg-gradient-to-br from-blue-50 to-green-50 dark:from-blue-950 dark:to-green-950">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Village to Skyline: Charting My Path to the City</CardTitle>
            <CardDescription>
              An interactive timeline of growth, challenges, and achievements
            </CardDescription>
          </CardHeader>
          <CardContent className="p-8">
            <div className="relative w-full h-96 bg-gradient-to-br from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 rounded-lg overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <svg width="100%" height="100%" className="absolute inset-0">
                  <defs>
                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="0.5"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
              </div>

              {/* Journey Path */}
              <svg className="absolute inset-0 w-full h-full">
                <motion.path
                  d={createPath()}
                  stroke="url(#gradient)"
                  strokeWidth="3"
                  fill="none"
                  strokeDasharray="5,5"
                  variants={pathVariants}
                />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#10b981" />
                    <stop offset="50%" stopColor="#3b82f6" />
                    <stop offset="100%" stopColor="#8b5cf6" />
                  </linearGradient>
                </defs>
              </svg>

              {/* Journey Steps */}
              {journeySteps.map((step, index) => (
                <motion.div
                  key={step.id}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                  style={{
                    left: `${step.coordinates.x}%`,
                    top: `${step.coordinates.y}%`,
                  }}
                  variants={stepVariants}
                  whileHover="hover"
                  onHoverStart={() => setHoveredStep(step.id)}
                  onHoverEnd={() => setHoveredStep(null)}
                  onClick={() => setSelectedStep(selectedStep === step.id ? null : step.id)}
                >
                  <div className={`relative w-12 h-12 rounded-full flex items-center justify-center text-2xl transition-all duration-300 ${
                    selectedStep === step.id || hoveredStep === step.id
                      ? 'bg-primary text-primary-foreground shadow-lg ring-4 ring-primary/30'
                      : 'bg-white dark:bg-gray-800 text-primary shadow-md hover:shadow-lg'
                  }`}>
                    {step.icon}
                    
                    {/* Step Number */}
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </div>
                  </div>

                  {/* Hover Tooltip */}
                  <AnimatePresence>
                    {hoveredStep === step.id && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.8 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.8 }}
                        className="absolute top-16 left-1/2 transform -translate-x-1/2 z-10"
                      >
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg border max-w-xs">
                          <Typography variant="small" className="font-medium">
                            {step.title}
                          </Typography>
                          <Typography variant="small" className="text-muted-foreground">
                            {step.period} • {step.location}
                          </Typography>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Detailed Step Information */}
      <AnimatePresence mode="wait">
        {selectedStep && (
          <motion.div
            key={selectedStep}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {(() => {
              const step = journeySteps.find(s => s.id === selectedStep);
              if (!step) return null;

              return (
                <Card className="overflow-hidden">
                  <div className="grid grid-cols-1 lg:grid-cols-2">
                    {/* Image */}
                    <div className="relative h-64 lg:h-full">
                      <Image
                        src={step.image}
                        alt={step.title}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
                      <div className="absolute bottom-4 left-4 text-white">
                        <Typography variant="h3" className="text-2xl font-bold">
                          {step.icon} {step.title}
                        </Typography>
                        <Typography variant="p" className="text-white/90">
                          {step.subtitle}
                        </Typography>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-8 space-y-6">
                      {/* Header */}
                      <div className="space-y-2">
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{step.period}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-4 w-4" />
                            <span>{step.location}</span>
                          </div>
                        </div>
                        <Typography variant="p" className="text-muted-foreground">
                          {step.description}
                        </Typography>
                      </div>

                      {/* Key Moments */}
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Target className="h-5 w-5 text-primary" />
                          <Typography variant="h4" className="font-semibold">Key Moments</Typography>
                        </div>
                        <ul className="space-y-2">
                          {step.keyMoments.map((moment, idx) => (
                            <li key={idx} className="flex items-start space-x-2 text-sm">
                              <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                              <span>{moment}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Emotions, Challenges, Learnings */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Heart className="h-4 w-4 text-red-500" />
                            <Typography variant="small" className="font-medium">Emotions</Typography>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {step.emotions.map((emotion, idx) => (
                              <span key={idx} className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-md text-xs">
                                {emotion}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-orange-500" />
                            <Typography variant="small" className="font-medium">Challenges</Typography>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {step.challenges.map((challenge, idx) => (
                              <span key={idx} className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 rounded-md text-xs">
                                {challenge}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Lightbulb className="h-4 w-4 text-green-500" />
                            <Typography variant="small" className="font-medium">Learnings</Typography>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {step.learnings.map((learning, idx) => (
                              <span key={idx} className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-md text-xs">
                                {learning}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Close Button */}
                      <div className="pt-4">
                        <Button
                          variant="outline"
                          onClick={() => setSelectedStep(null)}
                          className="w-full"
                        >
                          Close Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })()}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Journey Summary */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center"
      >
        <Card className="bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
          <CardContent className="p-8">
            <Typography variant="h3" className="mb-4">
              The Journey Continues
            </Typography>
            <Typography variant="p" className="text-muted-foreground max-w-2xl mx-auto">
              From a small village to the bustling world of technology and research, 
              every step has been a learning experience. The journey from limited resources 
              to contributing to cutting-edge research shows that with determination, 
              community support, and continuous learning, any dream is achievable.
            </Typography>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
