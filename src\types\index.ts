// Core types for the portfolio website

export interface Publication {
  id: string;
  title: string;
  authors: string[];
  journal: string;
  year: number;
  abstract: string;
  doi?: string;
  pdfUrl?: string;
  tags: string[];
  type: 'journal' | 'conference' | 'preprint' | 'thesis';
  featured?: boolean;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  technologies: string[];
  category: 'ai-ml' | 'web-dev' | 'research-tools' | 'open-source';
  imageUrl?: string;
  demoUrl?: string;
  githubUrl?: string;
  featured?: boolean;
  status: 'completed' | 'in-progress' | 'planned';
  startDate: string;
  endDate?: string;
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  publishedAt: string;
  updatedAt?: string;
  tags: string[];
  category: 'research' | 'technology' | 'career' | 'tutorials';
  readingTime: number;
  featured?: boolean;
  slug: string;
}

export interface TimelineEvent {
  id: string;
  title: string;
  organization: string;
  description: string;
  startDate: string;
  endDate?: string;
  type: 'education' | 'work' | 'research' | 'volunteering';
  location?: string;
  achievements?: string[];
  current?: boolean;
  status?: 'completed' | 'in-progress' | 'planned' | 'upcoming';
}

export interface VolunteeringExperience {
  id: string;
  organization: string;
  role: string;
  description: string;
  startDate: string;
  endDate?: string;
  location?: string;
  impact?: string;
  skills?: string[];
  current?: boolean;
}

export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  location: string;
  bio: string;
  elevator_pitch: string;
  social: {
    github?: string;
    linkedin?: string;
    twitter?: string;
    scholar?: string;
    orcid?: string;
    website?: string;
  };
  resume_url?: string;
  profile_image?: string;
}

export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Component prop types
export interface SectionProps {
  className?: string;
  children?: React.ReactNode;
}

export interface CardProps {
  className?: string;
  children: React.ReactNode;
  variant?: 'default' | 'outlined' | 'elevated';
}

export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  href?: string;
  external?: boolean;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  tags?: string[];
  category?: string;
  year?: number;
  type?: string;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Animation types
export interface AnimationProps {
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

// SEO types
export interface SEOProps {
  title: string;
  description: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article';
}
