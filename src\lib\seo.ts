import type { Metadata } from 'next';

// Base SEO configuration
export const siteConfig = {
  name: '<PERSON><PERSON> - AI Researcher & Software Engineer',
  description: 'Master\'s student in Computer Science (AI) at UESTC (ranked 3rd globally in AI), with 2 years of professional software engineering experience. Passionate about bridging academia and industry through innovative AI research and community impact.',
  url: 'https://premkatuwal.com',
  ogImage: 'https://premkatuwal.com/og-image.jpg',
  author: {
    name: 'Pre<PERSON> Katuwal',
    email: '<EMAIL>',
    twitter: '@premkatuwal',
    linkedin: 'https://linkedin.com/in/premkatuwal',
    github: 'https://github.com/Katwal-77',
  },
  keywords: [
    'AI Research',
    'Machine Learning',
    'Software Engineering',
    'PhD Student',
    'Computer Science',
    'Research Publications',
    'Open Source',
    'Community Volunteering',
    'Technology Education',
    'Academic Research',
    'Industry Experience',
    'Full Stack Development',
    'Data Science',
    'Artificial Intelligence',
    'Software Development',
  ],
};

// Generate metadata for pages
export function generateMetadata({
  title,
  description,
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  keywords = [],
}: {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  keywords?: string[];
}): Metadata {
  const metaTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name;
  const metaDescription = description || siteConfig.description;
  const metaImage = image || siteConfig.ogImage;
  const metaUrl = url ? `${siteConfig.url}${url}` : siteConfig.url;
  const allKeywords = [...siteConfig.keywords, ...keywords];

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: allKeywords.join(', '),
    authors: [{ name: siteConfig.author.name, url: siteConfig.url }],
    creator: siteConfig.author.name,
    publisher: siteConfig.author.name,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type,
      locale: 'en_US',
      url: metaUrl,
      title: metaTitle,
      description: metaDescription,
      siteName: siteConfig.name,
      images: [
        {
          url: metaImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
      ...(type === 'article' && {
        publishedTime,
        modifiedTime,
        authors: [siteConfig.author.name],
      }),
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [metaImage],
      creator: siteConfig.author.twitter,
      site: siteConfig.author.twitter,
    },
    alternates: {
      canonical: metaUrl,
    },
    other: {
      'google-site-verification': 'your-google-verification-code',
    },
  };
}

// Structured data generators
export function generatePersonStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: siteConfig.author.name,
    url: siteConfig.url,
    image: siteConfig.ogImage,
    sameAs: [
      siteConfig.author.linkedin,
      siteConfig.author.github,
      `https://twitter.com/${siteConfig.author.twitter.replace('@', '')}`,
    ],
    jobTitle: 'Master\'s Student & AI Researcher',
    worksFor: {
      '@type': 'Organization',
      name: 'University of Electronic Science and Technology of China (UESTC)',
    },
    alumniOf: {
      '@type': 'Organization',
      name: 'University of Electronic Science and Technology of China',
    },
    knowsAbout: [
      'Artificial Intelligence',
      'Machine Learning',
      'Software Engineering',
      'Computer Science Research',
      'Data Science',
      'Full Stack Development',
    ],
    email: siteConfig.author.email,
    description: siteConfig.description,
  };
}

export function generateWebsiteStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    url: siteConfig.url,
    description: siteConfig.description,
    author: {
      '@type': 'Person',
      name: siteConfig.author.name,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  };
}

export function generateBlogPostStructuredData({
  title,
  description,
  url,
  image,
  publishedAt,
  updatedAt,
  readingTime,
  tags,
}: {
  title: string;
  description: string;
  url: string;
  image?: string;
  publishedAt: string;
  updatedAt?: string;
  readingTime: number;
  tags: string[];
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: title,
    description,
    url: `${siteConfig.url}${url}`,
    image: image || siteConfig.ogImage,
    datePublished: publishedAt,
    dateModified: updatedAt || publishedAt,
    author: {
      '@type': 'Person',
      name: siteConfig.author.name,
      url: siteConfig.url,
    },
    publisher: {
      '@type': 'Person',
      name: siteConfig.author.name,
      url: siteConfig.url,
    },
    keywords: tags.join(', '),
    wordCount: readingTime * 200, // Approximate word count
    timeRequired: `PT${readingTime}M`,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${siteConfig.url}${url}`,
    },
  };
}

export function generateResearchArticleStructuredData({
  title,
  abstract,
  authors,
  publishedAt,
  journal,
  doi,
  url,
}: {
  title: string;
  abstract: string;
  authors: string[];
  publishedAt: string;
  journal?: string;
  doi?: string;
  url: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'ScholarlyArticle',
    headline: title,
    abstract,
    url: `${siteConfig.url}${url}`,
    datePublished: publishedAt,
    author: authors.map(author => ({
      '@type': 'Person',
      name: author,
    })),
    publisher: journal ? {
      '@type': 'Organization',
      name: journal,
    } : undefined,
    identifier: doi ? {
      '@type': 'PropertyValue',
      propertyID: 'DOI',
      value: doi,
    } : undefined,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${siteConfig.url}${url}`,
    },
  };
}

export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${siteConfig.url}${item.url}`,
    })),
  };
}
