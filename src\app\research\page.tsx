'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Typography } from '@/components/ui/Typography';
import { PublicationCard } from '@/components/features/research/PublicationCard';
import { SearchAndFilter } from '@/components/features/research/SearchAndFilter';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import publicationsData from '@/data/publications.json';
import type { Publication, SearchFilters } from '@/types';

export default function ResearchPage() {
  const [filters, setFilters] = useState<SearchFilters & { sortBy?: string; sortOrder?: string }>({
    query: '',
    tags: [],
    year: undefined,
    type: '',
    sortBy: 'year',
    sortOrder: 'desc',
  });

  // Extract unique values for filters
  const availableTags = useMemo(() => {
    const allTags = publicationsData.flatMap(pub => pub.tags);
    return Array.from(new Set(allTags)).sort();
  }, []);

  const availableYears = useMemo(() => {
    const years = publicationsData.map(pub => pub.year);
    return Array.from(new Set(years)).sort((a, b) => b - a);
  }, []);

  const availableTypes = useMemo(() => {
    const types = publicationsData.map(pub => pub.type);
    return Array.from(new Set(types));
  }, []);

  // Filter and sort publications
  const filteredPublications = useMemo(() => {
    let filtered = publicationsData.filter((pub: Publication) => {
      // Text search
      if (filters.query) {
        const searchText = filters.query.toLowerCase();
        const matchesTitle = pub.title.toLowerCase().includes(searchText);
        const matchesAuthors = pub.authors.some(author => 
          author.toLowerCase().includes(searchText)
        );
        const matchesAbstract = pub.abstract.toLowerCase().includes(searchText);
        const matchesTags = pub.tags.some(tag => 
          tag.toLowerCase().includes(searchText)
        );
        
        if (!matchesTitle && !matchesAuthors && !matchesAbstract && !matchesTags) {
          return false;
        }
      }

      // Type filter
      if (filters.type && pub.type !== filters.type) {
        return false;
      }

      // Year filter
      if (filters.year && pub.year !== filters.year) {
        return false;
      }

      // Tags filter
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag => pub.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    });

    // Sort publications
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'year':
          comparison = a.year - b.year;
          break;
        case 'citations':
          comparison = (a.citations || 0) - (b.citations || 0);
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        default:
          comparison = a.year - b.year;
      }

      return filters.sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [filters]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          Research & Publications
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          Explore my academic contributions to artificial intelligence, machine learning, 
          and computer science. Each publication represents a step forward in understanding 
          and advancing the field.
        </Typography>
      </motion.div>

      {/* Search and Filter */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
      >
        <SearchAndFilter
          onFiltersChange={setFilters}
          totalResults={filteredPublications.length}
          availableTags={availableTags}
          availableYears={availableYears}
          availableTypes={availableTypes}
        />
      </motion.div>

      {/* Publications Grid */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="grid grid-cols-1 lg:grid-cols-2 gap-8"
      >
        {filteredPublications.length > 0 ? (
          filteredPublications.map((publication, index) => (
            <PublicationCard
              key={publication.id}
              publication={publication}
              index={index}
            />
          ))
        ) : (
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="col-span-full text-center py-12"
          >
            <Typography variant="h3" className="text-muted-foreground mb-4">
              No publications found
            </Typography>
            <Typography variant="p" className="text-muted-foreground">
              Try adjusting your search criteria or filters to find relevant publications.
            </Typography>
          </motion.div>
        )}
      </motion.div>

      {/* Research Impact Summary */}
      {filteredPublications.length > 0 && (
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={ANIMATION_VARIANTS.slideUp}
          className="text-center pt-12 border-t"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {publicationsData.length}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Total Publications
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {publicationsData.reduce((sum, pub) => sum + (pub.citations || 0), 0)}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Total Citations
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {availableYears.length}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Years Active
              </Typography>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
