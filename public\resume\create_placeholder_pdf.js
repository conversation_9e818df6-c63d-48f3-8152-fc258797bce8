// Simple script to create a placeholder PDF
// This is a basic PDF structure that browsers can read

const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 24 Tf
100 700 Td
(Prem Katuwal - Resume Placeholder) Tj
0 -50 Td
/F1 12 Tf
(This is a placeholder resume file.) Tj
0 -20 Td
(Please replace with <PERSON><PERSON>'s actual professional resume.) Tj
0 -40 Td
(Master's student in Computer Science (AI) at UESTC) Tj
0 -20 Td
(2 years of professional software engineering experience) Tj
0 -20 Td
(Email: <EMAIL>) Tj
0 -20 Td
(GitHub: Katwal-77) Tj
0 -20 Td
(LinkedIn: prem<PERSON>uwal) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000526 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
593
%%EOF`;

// Write to file (this would need to be run in Node.js environment)
console.log('PDF content ready. Copy this to create Prem_Katuwal_Resume.pdf');
console.log('Or use the HTML file and convert to PDF using browser print function.');
