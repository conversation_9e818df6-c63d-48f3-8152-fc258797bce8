'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Mail, MapPin, Clock, Download, ExternalLink, Github, Linkedin, Twitter } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';

interface ContactMethod {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  value: string;
  action?: {
    label: string;
    href: string;
    external?: boolean;
  };
}

interface SocialLink {
  icon: React.ComponentType<{ className?: string }>;
  name: string;
  href: string;
  username: string;
}

export function ContactInfo() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const contactMethods: ContactMethod[] = [
    {
      icon: Mail,
      title: 'Email',
      description: 'Send me an email for any inquiries',
      value: '<EMAIL>',
      action: {
        label: 'Send Email',
        href: 'mailto:<EMAIL>',
      },
    },
    {
      icon: MapPin,
      title: 'Location',
      description: 'Currently based in',
      value: 'Chengdu, China',
    },
    {
      icon: Clock,
      title: 'Response Time',
      description: 'I typically respond within',
      value: '24-48 hours',
    },
  ];

  const socialLinks: SocialLink[] = [
    {
      icon: Github,
      name: 'GitHub',
      href: 'https://github.com/Katwal-77',
      username: 'Katwal-77',
    },
    {
      icon: Linkedin,
      name: 'LinkedIn',
      href: 'https://www.linkedin.com/in/premkatwal7/',
      username: '/in/premkatwal7',
    },
    {
      icon: Twitter,
      name: 'Twitter',
      href: 'https://twitter.com/premkatuwal',
      username: '@premkatuwal',
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-24 bg-muted/20 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      {/* Contact Methods */}
      <div className="space-y-4">
        {contactMethods.map((method) => (
          <motion.div key={method.title} variants={ANIMATION_VARIANTS.slideUp}>
            <Card className="hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <method.icon className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <Typography variant="h4" className="text-lg font-semibold mb-1">
                      {method.title}
                    </Typography>
                    <Typography variant="p" className="text-muted-foreground text-sm mb-2">
                      {method.description}
                    </Typography>
                    <Typography variant="p" className="font-medium">
                      {method.value}
                    </Typography>
                    {method.action && (
                      <div className="mt-3">
                        <Button
                          href={method.action.href}
                          variant="ghost"
                          size="sm"
                          external={method.action.external}
                          className="p-0 h-auto font-medium text-primary hover:text-primary/80"
                        >
                          {method.action.label}
                          <ExternalLink className="ml-1 h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Social Links */}
      <motion.div variants={ANIMATION_VARIANTS.slideUp}>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Connect on Social Media</CardTitle>
            <CardDescription>
              Follow me for updates on research, projects, and insights
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {socialLinks.map((social) => (
              <div key={social.name} className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <social.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <Typography variant="small" className="font-medium">
                      {social.name}
                    </Typography>
                    <Typography variant="small" className="text-muted-foreground">
                      {social.username}
                    </Typography>
                  </div>
                </div>
                <Button
                  href={social.href}
                  variant="ghost"
                  size="sm"
                  external
                  className="text-primary hover:text-primary/80"
                >
                  Follow
                  <ExternalLink className="ml-1 h-3 w-3" />
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Resume Download */}
      <motion.div variants={ANIMATION_VARIANTS.slideUp}>
        <Card className="bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
          <CardContent className="p-6 text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <Download className="h-8 w-8 text-primary" />
              </div>
              <div>
                <Typography variant="h4" className="text-lg font-semibold mb-2">
                  Download My Resume
                </Typography>
                <Typography variant="p" className="text-muted-foreground text-sm mb-4">
                  Get a comprehensive overview of my education, experience, and skills
                </Typography>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  href="/resume/Prem_Katuwal_Resume.pdf"
                  external
                  className="group"
                >
                  <Download className="mr-2 h-4 w-4 transition-transform group-hover:translate-y-0.5" />
                  Download PDF
                </Button>
                <Button
                  href="/cv.pdf"
                  variant="outline"
                  external
                  className="group"
                >
                  <Download className="mr-2 h-4 w-4 transition-transform group-hover:translate-y-0.5" />
                  Academic CV
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Availability Status */}
      <motion.div variants={ANIMATION_VARIANTS.slideUp}>
        <Card className="border-green-200 dark:border-green-800">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <div>
                <Typography variant="small" className="font-medium text-green-700 dark:text-green-300">
                  Currently Available
                </Typography>
                <Typography variant="small" className="text-muted-foreground">
                  Open to research collaborations, consulting opportunities, and speaking engagements
                </Typography>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
