'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Mail, MapPin, Clock, Download, ExternalLink, Github, Linkedin, Twitter } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';

interface ContactMethod {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  value: string;
  action?: {
    label: string;
    href: string;
    external?: boolean;
  };
}

interface SocialLink {
  icon: React.ComponentType<{ className?: string }>;
  name: string;
  href: string;
  username: string;
}

export function ContactInfo() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const contactMethods: ContactMethod[] = [
    {
      icon: Mail,
      title: 'Email',
      description: 'Send me an email for any inquiries',
      value: '<EMAIL>',
      action: {
        label: 'Send Email',
        href: 'mailto:<EMAIL>',
      },
    },
    {
      icon: MapPin,
      title: 'Location',
      description: 'Currently based in',
      value: 'Chengdu, China',
    },
    {
      icon: Clock,
      title: 'Response Time',
      description: 'I typically respond within',
      value: '24-48 hours',
    },
  ];

  const socialLinks: SocialLink[] = [
    {
      icon: Github,
      name: 'GitHub',
      href: 'https://github.com/Katwal-77',
      username: 'Katwal-77',
    },
    {
      icon: Linkedin,
      name: 'LinkedIn',
      href: 'https://www.linkedin.com/in/premkatwal7/',
      username: '/in/premkatwal7',
    },
    {
      icon: Twitter,
      name: 'Twitter',
      href: 'https://twitter.com/premkatawal',
      username: '@premkatawal',
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 25,
      }
    },
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -90 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 20,
        delay: 0.2,
      }
    },
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-24 bg-muted/20 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="space-y-6"
    >
      {/* Contact Methods */}
      <div className="space-y-6">
        {contactMethods.map((method) => (
          <motion.div
            key={method.title}
            variants={cardVariants}
            whileHover={{ y: -5, scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 25 }}
          >
            <Card className="backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-xl hover:border-primary/20 transition-all duration-500 group overflow-hidden">
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <motion.div
                    className="flex-shrink-0"
                    variants={iconVariants}
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl flex items-center justify-center group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300">
                      <method.icon className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-300" />
                    </div>
                  </motion.div>
                  <div className="flex-1 min-w-0 space-y-3">
                    <Typography variant="h4" className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                      {method.title}
                    </Typography>
                    <Typography variant="p" className="text-muted-foreground text-base leading-relaxed">
                      {method.description}
                    </Typography>
                    <Typography variant="p" className="font-semibold text-lg text-foreground">
                      {method.value}
                    </Typography>
                    {method.action && (
                      <motion.div
                        className="mt-4"
                        whileHover={{ x: 5 }}
                        transition={{ type: "spring", stiffness: 400, damping: 25 }}
                      >
                        <Button
                          href={method.action.href}
                          variant="ghost"
                          size="sm"
                          external={method.action.external}
                          className="p-0 h-auto font-semibold text-primary hover:text-primary/80 group/button"
                        >
                          {method.action.label}
                          <ExternalLink className="ml-2 h-4 w-4 group-hover/button:translate-x-1 transition-transform duration-200" />
                        </Button>
                      </motion.div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Social Links */}
      <motion.div variants={cardVariants}>
        <Card className="backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-xl transition-all duration-500">
          <CardHeader className="pb-6">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Connect on Social Media
              </CardTitle>
              <CardDescription className="text-base mt-2 leading-relaxed">
                Follow me for updates on research, projects, and insights
              </CardDescription>
            </motion.div>
          </CardHeader>
          <CardContent className="space-y-4">
            {socialLinks.map((social, index) => (
              <motion.div
                key={social.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.1 }}
                whileHover={{ x: 5, scale: 1.02 }}
                className="group"
              >
                <div className="flex items-center justify-between p-4 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-accent/5 transition-all duration-300 border border-transparent hover:border-primary/20">
                  <div className="flex items-center space-x-4">
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-br from-primary/10 to-accent/10 rounded-xl flex items-center justify-center group-hover:from-primary/20 group-hover:to-accent/20 transition-all duration-300"
                      whileHover={{ rotate: 5, scale: 1.1 }}
                    >
                      <social.icon className="h-6 w-6 text-primary group-hover:scale-110 transition-transform duration-300" />
                    </motion.div>
                    <div>
                      <Typography variant="small" className="font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                        {social.name}
                      </Typography>
                      <Typography variant="small" className="text-muted-foreground text-sm">
                        {social.username}
                      </Typography>
                    </div>
                  </div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      href={social.href}
                      variant="ghost"
                      size="sm"
                      external
                      className="text-primary hover:text-primary/80 font-semibold group/follow"
                    >
                      Follow
                      <ExternalLink className="ml-2 h-4 w-4 group-hover/follow:translate-x-1 transition-transform duration-200" />
                    </Button>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Resume Download */}
      <motion.div variants={cardVariants}>
        <Card className="bg-gradient-to-br from-primary/10 to-accent/10 border-primary/30 hover:shadow-xl transition-all duration-500 overflow-hidden group">
          <CardContent className="p-8 text-center relative">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <div className="relative space-y-6">
              <motion.div
                className="w-20 h-20 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl flex items-center justify-center mx-auto group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300"
                whileHover={{ rotate: 5, scale: 1.1 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
              >
                <Download className="h-10 w-10 text-primary group-hover:scale-110 transition-transform duration-300" />
              </motion.div>
              <div>
                <Typography variant="h4" className="text-2xl font-bold mb-3 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                  Download My Resume
                </Typography>
                <Typography variant="p" className="text-muted-foreground text-base leading-relaxed">
                  Get a comprehensive overview of my education, experience, and skills
                </Typography>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    href="/resume/Prem_Katuwal_Resume.pdf"
                    external
                    size="lg"
                    className="group/btn bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl"
                  >
                    <Download className="mr-3 h-5 w-5 transition-transform group-hover/btn:translate-y-1" />
                    Download PDF
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    href="/cv.pdf"
                    variant="outline"
                    size="lg"
                    external
                    className="group/btn border-2 border-primary/50 hover:border-primary hover:bg-primary/5"
                  >
                    <Download className="mr-3 h-5 w-5 transition-transform group-hover/btn:translate-y-1" />
                    Academic CV
                  </Button>
                </motion.div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Availability Status */}
      <motion.div variants={cardVariants}>
        <Card className="border-2 border-green-200 dark:border-green-800 bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-900/20 dark:to-emerald-900/20 hover:shadow-xl transition-all duration-500 group">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <motion.div
                className="flex-shrink-0 mt-1"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                <div className="w-4 h-4 bg-green-500 rounded-full shadow-lg shadow-green-500/50"></div>
              </motion.div>
              <div className="space-y-2">
                <Typography variant="small" className="font-bold text-green-700 dark:text-green-300 text-base">
                  Currently Available
                </Typography>
                <Typography variant="small" className="text-muted-foreground leading-relaxed">
                  Open to research collaborations, consulting opportunities, and speaking engagements
                </Typography>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
