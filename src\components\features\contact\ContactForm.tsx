'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, CheckCircle, AlertCircle, Loader2, User, Mail, MessageSquare, Tag } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input, Textarea, Label } from '@/components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  inquiryType: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
}

export function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
    inquiryType: 'general',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const inquiryTypes = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'collaboration', label: 'Research Collaboration' },
    { value: 'consulting', label: 'Consulting Opportunity' },
    { value: 'speaking', label: 'Speaking Engagement' },
    { value: 'mentoring', label: 'Mentoring Request' },
    { value: 'other', label: 'Other' },
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Subject validation
    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    } else if (formData.subject.trim().length < 5) {
      newErrors.subject = 'Subject must be at least 5 characters';
    }

    // Message validation
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Simulate API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // For now, we'll just log the form data
      console.log('Form submitted:', formData);
      
      setSubmitStatus('success');
      
      // Reset form after successful submission
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        inquiryType: 'general',
      });
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const formFieldVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 24,
      }
    },
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 20,
        delay: 0.1,
      }
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="w-full"
    >
      <Card className="max-w-2xl mx-auto backdrop-blur-sm bg-card/95 border-border/50 shadow-xl hover:shadow-2xl transition-all duration-500">
        <CardHeader className="space-y-6 pb-8">
          <motion.div variants={formFieldVariants} className="text-center space-y-4">
            <motion.div
              variants={iconVariants}
              className="w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl flex items-center justify-center mx-auto"
            >
              <MessageSquare className="h-8 w-8 text-primary" />
            </motion.div>
            <div>
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Send Me a Message
              </CardTitle>
              <CardDescription className="text-lg mt-3 leading-relaxed">
                I'd love to hear from you! Whether you have a question, collaboration idea,
                or just want to connect, feel free to reach out.
              </CardDescription>
            </div>
          </motion.div>
        </CardHeader>

        <CardContent className="px-8 pb-8">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Name and Email Row */}
            <motion.div
              variants={formFieldVariants}
              className="grid grid-cols-1 md:grid-cols-2 gap-6"
            >
              <motion.div
                className="space-y-3"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Label htmlFor="name" className="text-sm font-semibold text-foreground/80 flex items-center gap-2">
                  <User className="h-4 w-4 text-primary" />
                  Name *
                </Label>
                <div className="relative group">
                  <Input
                    id="name"
                    type="text"
                    placeholder="Your full name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`h-12 px-4 text-base transition-all duration-300 border-2 focus:border-primary/50 hover:border-primary/30 ${
                      errors.name
                        ? 'border-red-500 focus:border-red-500'
                        : 'border-border/50 focus:border-primary'
                    } ${isSubmitting ? 'opacity-50' : ''}`}
                    disabled={isSubmitting}
                  />
                  <div className="absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
                <AnimatePresence>
                  {errors.name && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Typography variant="small" className="text-red-500 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        {errors.name}
                      </Typography>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>

              <motion.div
                className="space-y-3"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Label htmlFor="email" className="text-sm font-semibold text-foreground/80 flex items-center gap-2">
                  <Mail className="h-4 w-4 text-primary" />
                  Email *
                </Label>
                <div className="relative group">
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`h-12 px-4 text-base transition-all duration-300 border-2 focus:border-primary/50 hover:border-primary/30 ${
                      errors.email
                        ? 'border-red-500 focus:border-red-500'
                        : 'border-border/50 focus:border-primary'
                    } ${isSubmitting ? 'opacity-50' : ''}`}
                    disabled={isSubmitting}
                  />
                  <div className="absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
                <AnimatePresence>
                  {errors.email && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Typography variant="small" className="text-red-500 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        {errors.email}
                      </Typography>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </motion.div>

            {/* Inquiry Type */}
            <motion.div
              variants={formFieldVariants}
              className="space-y-3"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <Label htmlFor="inquiryType" className="text-sm font-semibold text-foreground/80 flex items-center gap-2">
                <Tag className="h-4 w-4 text-primary" />
                Type of Inquiry
              </Label>
              <div className="relative group">
                <select
                  id="inquiryType"
                  value={formData.inquiryType}
                  onChange={(e) => handleInputChange('inquiryType', e.target.value)}
                  className="w-full h-12 rounded-md border-2 border-border/50 bg-background px-4 text-base ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus:border-primary hover:border-primary/30 transition-all duration-300 disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={isSubmitting}
                >
                  {inquiryTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
            </motion.div>

            {/* Subject */}
            <motion.div
              variants={formFieldVariants}
              className="space-y-3"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <Label htmlFor="subject" className="text-sm font-semibold text-foreground/80 flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-primary" />
                Subject *
              </Label>
              <div className="relative group">
                <Input
                  id="subject"
                  type="text"
                  placeholder="Brief description of your inquiry"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  className={`h-12 px-4 text-base transition-all duration-300 border-2 focus:border-primary/50 hover:border-primary/30 ${
                    errors.subject
                      ? 'border-red-500 focus:border-red-500'
                      : 'border-border/50 focus:border-primary'
                  } ${isSubmitting ? 'opacity-50' : ''}`}
                  disabled={isSubmitting}
                />
                <div className="absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
              <AnimatePresence>
                {errors.subject && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Typography variant="small" className="text-red-500 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      {errors.subject}
                    </Typography>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Message */}
            <motion.div
              variants={formFieldVariants}
              className="space-y-3"
              whileHover={{ scale: 1.01 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <Label htmlFor="message" className="text-sm font-semibold text-foreground/80 flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-primary" />
                Message *
              </Label>
              <div className="relative group">
                <Textarea
                  id="message"
                  placeholder="Tell me more about your inquiry, project, or how I can help..."
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  className={`min-h-[140px] px-4 py-3 text-base transition-all duration-300 border-2 focus:border-primary/50 hover:border-primary/30 resize-none ${
                    errors.message
                      ? 'border-red-500 focus:border-red-500'
                      : 'border-border/50 focus:border-primary'
                  } ${isSubmitting ? 'opacity-50' : ''}`}
                  disabled={isSubmitting}
                />
                <div className="absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
              <div className="flex justify-between items-center">
                <AnimatePresence>
                  {errors.message && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Typography variant="small" className="text-red-500 flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        {errors.message}
                      </Typography>
                    </motion.div>
                  )}
                </AnimatePresence>
                <Typography variant="small" className="text-muted-foreground ml-auto">
                  {formData.message.length}/500 characters
                </Typography>
              </div>
            </motion.div>

            {/* Submit Button */}
            <motion.div
              variants={formFieldVariants}
              className="space-y-4 pt-4"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Button
                  type="submit"
                  size="lg"
                  className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transition-all duration-300"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                      Sending Message...
                    </>
                  ) : (
                    <>
                      <Send className="mr-3 h-5 w-5" />
                      Send Message
                    </>
                  )}
                </Button>
              </motion.div>

              {/* Status Messages */}
              <AnimatePresence>
                {submitStatus === 'success' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    className="flex items-center gap-3 text-green-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-xl border border-green-200 dark:border-green-800 shadow-sm"
                  >
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 400, damping: 20 }}
                    >
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </motion.div>
                    <Typography variant="small" className="font-medium">
                      Thank you! Your message has been sent successfully. I'll get back to you soon.
                    </Typography>
                  </motion.div>
                )}

                {submitStatus === 'error' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    className="flex items-center gap-3 text-red-700 bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 p-4 rounded-xl border border-red-200 dark:border-red-800 shadow-sm"
                  >
                    <motion.div
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 400, damping: 20 }}
                    >
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    </motion.div>
                    <Typography variant="small" className="font-medium">
                      Sorry, there was an error sending your message. Please try again or contact me directly.
                    </Typography>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
