{"name": "pf", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky install", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:accessibility": "jest --testPathPattern=accessibility", "test:performance": "jest --testPathPattern=performance", "test:integration": "jest --testPathPattern=integration", "deploy": "npm run build && vercel --prod", "deploy:preview": "vercel", "deploy:check": "bash scripts/deploy.sh", "analyze": "cross-env ANALYZE=true npm run build", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "sitemap": "next-sitemap"}, "dependencies": {"@types/mdx": "^2.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "gray-matter": "^4.0.3", "lucide-react": "^0.522.0", "next": "15.3.4", "next-mdx-remote": "^5.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-testing-library": "^7.5.3", "husky": "^9.1.7", "jest": "^30.0.2", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.2", "lint-staged": "^16.1.2", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "typescript": "^5"}}