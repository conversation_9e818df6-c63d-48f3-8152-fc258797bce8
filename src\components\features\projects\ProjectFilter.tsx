'use client';

import { useState } from 'react';
import { Filter, X, Grid, List } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { PROJECT_CATEGORIES } from '@/lib/constants';

interface ProjectFilterProps {
  onFilterChange: (filters: {
    category: string;
    status: string;
    technologies: string[];
    featured: boolean;
  }) => void;
  totalResults: number;
  availableTechnologies: string[];
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
}

export function ProjectFilter({
  onFilterChange,
  totalResults,
  availableTechnologies,
  viewMode,
  onViewModeChange,
}: ProjectFilterProps) {
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    technologies: [] as string[],
    featured: false,
  });
  const [showFilters, setShowFilters] = useState(false);

  const updateFilters = (newFilters: Partial<typeof filters>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFilterChange(updatedFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      category: '',
      status: '',
      technologies: [],
      featured: false,
    };
    setFilters(clearedFilters);
    onFilterChange(clearedFilters);
  };

  const toggleTechnology = (tech: string) => {
    const newTechnologies = filters.technologies.includes(tech)
      ? filters.technologies.filter(t => t !== tech)
      : [...filters.technologies, tech];
    updateFilters({ technologies: newTechnologies });
  };

  const hasActiveFilters = filters.category || filters.status || filters.technologies.length > 0 || filters.featured;

  const categories = Object.entries(PROJECT_CATEGORIES);
  const statuses = [
    { value: 'completed', label: 'Completed' },
    { value: 'in-progress', label: 'In Progress' },
    { value: 'planned', label: 'Planned' },
  ];

  return (
    <div className="space-y-4">
      {/* Filter Controls */}
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 text-xs flex items-center justify-center">
                {(filters.category ? 1 : 0) + 
                 (filters.status ? 1 : 0) + 
                 filters.technologies.length + 
                 (filters.featured ? 1 : 0)}
              </span>
            )}
          </Button>

          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-4">
          <Typography variant="small" className="text-muted-foreground">
            {totalResults} project{totalResults !== 1 ? 's' : ''}
          </Typography>
          
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="p-4 bg-muted/50 rounded-lg border space-y-4">
          {/* Category Filter */}
          <div className="space-y-2">
            <Typography variant="small" className="font-medium">Category</Typography>
            <div className="flex flex-wrap gap-2">
              {categories.map(([key, label]) => (
                <Button
                  key={key}
                  variant={filters.category === key ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilters({ category: filters.category === key ? '' : key })}
                >
                  {label}
                </Button>
              ))}
            </div>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Typography variant="small" className="font-medium">Status</Typography>
            <div className="flex flex-wrap gap-2">
              {statuses.map((status) => (
                <Button
                  key={status.value}
                  variant={filters.status === status.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilters({ status: filters.status === status.value ? '' : status.value })}
                >
                  {status.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Featured Filter */}
          <div className="space-y-2">
            <Typography variant="small" className="font-medium">Special</Typography>
            <Button
              variant={filters.featured ? "default" : "outline"}
              size="sm"
              onClick={() => updateFilters({ featured: !filters.featured })}
            >
              ⭐ Featured Projects
            </Button>
          </div>

          {/* Technology Filter */}
          <div className="space-y-2">
            <Typography variant="small" className="font-medium">Technologies</Typography>
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {availableTechnologies.slice(0, 20).map((tech) => (
                <Button
                  key={tech}
                  variant={filters.technologies.includes(tech) ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleTechnology(tech)}
                >
                  {tech}
                </Button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
