'use client';

import { motion, useInView } from 'framer-motion';
import { useRef, useEffect, useState } from 'react';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';

interface Stat {
  id: string;
  value: number;
  label: string;
  suffix?: string;
  prefix?: string;
  description?: string;
}

interface StatsProps {
  stats: Stat[];
}

function AnimatedCounter({ 
  value, 
  duration = 2000 
}: { 
  value: number; 
  duration?: number; 
}) {
  const [count, setCount] = useState(0);
  const ref = useRef<HTMLSpanElement>(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  useEffect(() => {
    if (!isInView) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(Math.floor(easeOutQuart * value));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isInView, value, duration]);

  return <span ref={ref}>{count}</span>;
}

export function Stats({ stats }: StatsProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="space-y-12"
        >
          {/* Section Header */}
          <motion.div variants={ANIMATION_VARIANTS.slideUp} className="text-center space-y-4">
            <Typography variant="h2" className="text-3xl lg:text-4xl font-bold">
              Impact & Achievements
            </Typography>
            <Typography variant="lead" className="max-w-2xl mx-auto">
              Numbers that reflect my commitment to research excellence, 
              community engagement, and professional growth.
            </Typography>
          </motion.div>

          {/* Stats Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.id}
                variants={ANIMATION_VARIANTS.slideUp}
                className="text-center space-y-2 group"
              >
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  {/* Background Circle */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300" />
                  
                  {/* Stat Value */}
                  <div className="relative bg-background border border-border rounded-full w-32 h-32 mx-auto flex items-center justify-center group-hover:border-primary/50 transition-colors duration-300">
                    <Typography variant="h2" className="text-2xl lg:text-3xl font-bold text-primary">
                      {stat.prefix}
                      <AnimatedCounter value={stat.value} />
                      {stat.suffix}
                    </Typography>
                  </div>
                </motion.div>

                {/* Stat Label */}
                <div className="space-y-1">
                  <Typography variant="h4" className="text-lg font-semibold">
                    {stat.label}
                  </Typography>
                  {stat.description && (
                    <Typography variant="muted" className="text-sm">
                      {stat.description}
                    </Typography>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Context */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="text-center pt-8"
          >
            <div className="max-w-4xl mx-auto p-6 bg-muted/50 rounded-lg border">
              <Typography variant="p" className="text-muted-foreground">
                These metrics represent my journey in academia and technology, 
                showcasing not just individual achievements but also my commitment 
                to collaborative research, community building, and knowledge sharing. 
                Each number tells a story of dedication, learning, and impact.
              </Typography>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
