'use client';

import { motion } from 'framer-motion';
import { Calendar, MapPin, Award, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';
import { Button } from '@/components/ui/Button';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import type { TimelineEvent } from '@/types';

interface TimelineProps {
  events: TimelineEvent[];
  title?: string;
  description?: string;
}

export function Timeline({ events, title = "My Journey", description }: TimelineProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'education':
        return '🎓';
      case 'work':
        return '💼';
      case 'research':
        return '🔬';
      case 'volunteering':
        return '🤝';
      default:
        return '📋';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'education':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'work':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'research':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'volunteering':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  };

  const sortedEvents = [...events].sort((a, b) => 
    new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  );

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="space-y-12"
        >
          {/* Section Header */}
          <motion.div variants={ANIMATION_VARIANTS.slideUp} className="text-center space-y-4">
            <Typography variant="h2" className="text-3xl lg:text-4xl font-bold">
              {title}
            </Typography>
            {description && (
              <Typography variant="lead" className="max-w-2xl mx-auto">
                {description}
              </Typography>
            )}
          </motion.div>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-px bg-border transform md:-translate-x-px" />

            <motion.div variants={containerVariants} className="space-y-8">
              {sortedEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  variants={ANIMATION_VARIANTS.slideUp}
                  className={`relative flex items-center ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  }`}
                >
                  {/* Timeline Dot */}
                  <div className="absolute left-4 md:left-1/2 w-3 h-3 bg-primary rounded-full transform -translate-x-1/2 z-10">
                    <div className="absolute inset-0 bg-primary rounded-full animate-ping opacity-75" />
                  </div>

                  {/* Content */}
                  <div className={`w-full md:w-1/2 ${
                    index % 2 === 0 ? 'md:pr-8 pl-12 md:pl-0' : 'md:pl-8 pl-12 md:pr-0'
                  }`}>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="hover:shadow-lg transition-all duration-300">
                        <CardHeader className="space-y-3">
                          <div className="flex items-center justify-between flex-wrap gap-2">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(
                                event.type
                              )}`}
                            >
                              <span className="mr-1">{getTypeIcon(event.type)}</span>
                              {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                            </span>
                            {event.current && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                                Current
                              </span>
                            )}
                            {event.status === 'upcoming' && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-200 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 ml-2">
                                Upcoming
                              </span>
                            )}
                            {event.status === 'planned' && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-200 text-blue-800 dark:bg-blue-900 dark:text-blue-200 ml-2">
                                Planned
                              </span>
                            )}
                          </div>

                          <CardTitle className="text-xl">{event.title}</CardTitle>
                          <CardDescription className="text-lg font-medium text-primary">
                            {event.organization}
                          </CardDescription>

                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>
                                {formatDate(event.startDate)} - {
                                  event.endDate ? formatDate(event.endDate) : 'Present'
                                }
                              </span>
                            </div>
                            {event.location && (
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-4 w-4" />
                                <span>{event.location}</span>
                              </div>
                            )}
                          </div>
                        </CardHeader>

                        <CardContent className="space-y-4">
                          <Typography variant="p" className="text-muted-foreground">
                            {event.description}
                          </Typography>

                          {event.achievements && event.achievements.length > 0 && (
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Award className="h-4 w-4 text-accent" />
                                <Typography variant="small" className="font-medium">
                                  Key Achievements
                                </Typography>
                              </div>
                              <ul className="space-y-1 ml-6">
                                {event.achievements.map((achievement, idx) => (
                                  <li key={idx} className="text-sm text-muted-foreground">
                                    • {achievement}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </motion.div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
