'use client';

import { motion } from 'framer-motion';
import { ExternalLink, Download, Quote, Users, Calendar, Award } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import type { Publication } from '@/types';

interface PublicationCardProps {
  publication: Publication;
  index?: number;
}

export function PublicationCard({ publication, index = 0 }: PublicationCardProps) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'journal':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'conference':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'preprint':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'thesis':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'journal':
        return '📄';
      case 'conference':
        return '🎤';
      case 'preprint':
        return '📝';
      case 'thesis':
        return '🎓';
      default:
        return '📋';
    }
  };

  const formatAuthors = (authors: string[]) => {
    if (authors.length <= 3) {
      return authors.join(', ');
    }
    return `${authors.slice(0, 3).join(', ')}, et al.`;
  };

  const formatCitation = () => {
    const authorList = formatAuthors(publication.authors);
    const year = publication.year;
    const title = publication.title;
    const venue = publication.venue || publication.journal;
    
    return `${authorList} (${year}). ${title}. ${venue}.`;
  };

  return (
    <motion.div
      variants={ANIMATION_VARIANTS.slideUp}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full hover:shadow-lg transition-all duration-300 group">
        <CardHeader className="space-y-4">
          {/* Header with badges */}
          <div className="flex items-center justify-between flex-wrap gap-2">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(
                publication.type
              )}`}
            >
              <span className="mr-1">{getTypeIcon(publication.type)}</span>
              {publication.type.charAt(0).toUpperCase() + publication.type.slice(1)}
            </span>
            <div className="flex items-center space-x-2">
              {publication.featured && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                  ⭐ Featured
                </span>
              )}
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary text-secondary-foreground">
                {publication.year}
              </span>
            </div>
          </div>

          {/* Title */}
          <CardTitle className="text-xl group-hover:text-primary transition-colors leading-tight">
            {publication.title}
          </CardTitle>

          {/* Authors and venue */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>{formatAuthors(publication.authors)}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span className="font-medium text-primary">{publication.venue || publication.journal}</span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Abstract */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Quote className="h-4 w-4 text-muted-foreground" />
              <Typography variant="small" className="font-medium">Abstract</Typography>
            </div>
            <Typography variant="p" className="text-sm text-muted-foreground line-clamp-4">
              {publication.abstract}
            </Typography>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            {publication.tags.slice(0, 4).map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground"
              >
                {tag}
              </span>
            ))}
            {publication.tags.length > 4 && (
              <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground">
                +{publication.tags.length - 4} more
              </span>
            )}
          </div>

          {/* Citation count */}
          {publication.citations && publication.citations > 0 && (
            <div className="flex items-center space-x-2 text-sm">
              <Award className="h-4 w-4 text-accent" />
              <span className="text-muted-foreground">
                <strong className="text-accent">{publication.citations}</strong> citations
              </span>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex items-center space-x-2 pt-2">
            {publication.doi && (
              <Button
                href={`https://doi.org/${publication.doi}`}
                variant="ghost"
                size="sm"
                external
                className="flex-1 group/btn"
              >
                View Paper
                <ExternalLink className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-0.5" />
              </Button>
            )}
            {publication.pdfUrl && (
              <Button
                href={publication.pdfUrl}
                variant="ghost"
                size="sm"
                external
                className="group/btn"
              >
                <Download className="h-3 w-3 transition-transform group-hover/btn:translate-y-0.5" />
              </Button>
            )}
          </div>

          {/* Citation format */}
          <div className="pt-4 border-t">
            <Typography variant="small" className="text-xs text-muted-foreground font-mono leading-relaxed">
              {formatCitation()}
            </Typography>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
