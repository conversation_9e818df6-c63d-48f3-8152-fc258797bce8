'use client';

import { motion } from 'framer-motion';
import { Typography } from '@/components/ui/Typography';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { ANIMATION_VARIANTS } from '@/lib/constants';

interface Skill {
  name: string;
  level: number; // 1-100
  category: string;
}

interface SkillCategory {
  name: string;
  icon: string;
  skills: Skill[];
}

interface SkillsProps {
  skillCategories: SkillCategory[];
}

export function Skills({ skillCategories }: SkillsProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const SkillBar = ({ skill }: { skill: Skill }) => (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <Typography variant="small" className="font-medium">
          {skill.name}
        </Typography>
        <Typography variant="small" className="text-muted-foreground">
          {skill.level}%
        </Typography>
      </div>
      <div className="w-full bg-muted rounded-full h-2">
        <motion.div
          className="bg-gradient-to-r from-primary to-accent h-2 rounded-full"
          initial={{ width: 0 }}
          whileInView={{ width: `${skill.level}%` }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.2 }}
        />
      </div>
    </div>
  );

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="space-y-12"
        >
          {/* Section Header */}
          <motion.div variants={ANIMATION_VARIANTS.slideUp} className="text-center space-y-4">
            <Typography variant="h2" className="text-3xl lg:text-4xl font-bold">
              Technical Expertise
            </Typography>
            <Typography variant="lead" className="max-w-2xl mx-auto">
              A comprehensive overview of my technical skills, developed through 
              academic research, industry experience, and continuous learning.
            </Typography>
          </motion.div>

          {/* Skills Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {skillCategories.map((category, index) => (
              <motion.div
                key={category.name}
                variants={ANIMATION_VARIANTS.slideUp}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300">
                  <CardHeader className="text-center space-y-3">
                    <div className="text-4xl">{category.icon}</div>
                    <CardTitle className="text-xl">{category.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {category.skills.map((skill) => (
                      <SkillBar key={skill.name} skill={skill} />
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Context */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="text-center pt-8"
          >
            <div className="max-w-4xl mx-auto p-6 bg-background rounded-lg border">
              <Typography variant="p" className="text-muted-foreground">
                These skills represent years of dedicated learning and practical application. 
                I'm constantly expanding my expertise through research projects, industry collaboration, 
                and staying current with emerging technologies in AI and software development.
              </Typography>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
