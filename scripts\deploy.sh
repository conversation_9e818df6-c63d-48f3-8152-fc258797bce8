#!/bin/bash

# Deployment script for Prem Katuwal's Portfolio
# This script handles pre-deployment checks and optimizations

set -e

echo "🚀 Starting deployment process for Prem Katuwal's Portfolio..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version)
print_status "Node.js version: $NODE_VERSION"

# Check if required environment variables are set
if [ -z "$NEXT_PUBLIC_SITE_URL" ]; then
    print_warning "NEXT_PUBLIC_SITE_URL not set. Using default."
fi

# Install dependencies
print_status "Installing dependencies..."
npm ci --production=false

# Run type checking
print_status "Running TypeScript type checking..."
npm run type-check

# Run linting
print_status "Running ESLint..."
npm run lint

# Run tests
print_status "Running tests..."
npm run test:ci

# Check for security vulnerabilities
print_status "Checking for security vulnerabilities..."
npm audit --audit-level=high

# Build the application
print_status "Building the application..."
npm run build

# Check build size
print_status "Analyzing build size..."
if [ -d ".next" ]; then
    BUILD_SIZE=$(du -sh .next | cut -f1)
    print_status "Build size: $BUILD_SIZE"
fi

# Generate sitemap (if not already generated)
print_status "Generating sitemap..."
if [ -f ".next/server/app/sitemap.xml/route.js" ]; then
    print_success "Sitemap generation configured"
else
    print_warning "Sitemap generation not found"
fi

# Optimize images (if any new ones)
print_status "Checking image optimization..."
if command -v imagemin &> /dev/null; then
    find public -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | head -5 | while read img; do
        print_status "Optimized: $img"
    done
else
    print_warning "imagemin not installed. Consider optimizing images manually."
fi

# Check for large files
print_status "Checking for large files..."
find . -name "*.js" -o -name "*.css" -o -name "*.json" | xargs ls -la | awk '$5 > 1000000 {print $9 " (" $5 " bytes)"}' | head -5

# Performance check
print_status "Running performance checks..."
if [ -f ".next/analyze/client.html" ]; then
    print_success "Bundle analysis available"
fi

# Security headers check
print_status "Verifying security configuration..."
if [ -f "vercel.json" ]; then
    if grep -q "X-Frame-Options" vercel.json; then
        print_success "Security headers configured"
    else
        print_warning "Security headers not found in vercel.json"
    fi
fi

# Check for sensitive data
print_status "Checking for sensitive data..."
if grep -r "password\|secret\|key" --include="*.js" --include="*.ts" --include="*.json" . | grep -v node_modules | grep -v ".git" | head -3; then
    print_warning "Potential sensitive data found. Please review."
fi

# Final checks
print_status "Running final checks..."

# Check if all required files exist
REQUIRED_FILES=("package.json" "next.config.ts" "tailwind.config.ts" "tsconfig.json")
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_success "✓ $file exists"
    else
        print_error "✗ $file missing"
        exit 1
    fi
done

# Check if build was successful
if [ -d ".next" ] && [ -f ".next/BUILD_ID" ]; then
    BUILD_ID=$(cat .next/BUILD_ID)
    print_success "Build completed successfully (ID: $BUILD_ID)"
else
    print_error "Build failed or incomplete"
    exit 1
fi

print_success "🎉 Pre-deployment checks completed successfully!"
print_status "Ready for deployment to Vercel"

# Display deployment information
echo ""
echo "📋 Deployment Summary:"
echo "======================"
echo "Project: Prem Katuwal's Portfolio"
echo "Node.js: $NODE_VERSION"
echo "Build ID: $BUILD_ID"
echo "Build Size: $BUILD_SIZE"
echo "Environment: ${NODE_ENV:-development}"
echo ""

# Instructions for manual deployment
echo "🔧 Next Steps:"
echo "=============="
echo "1. Push your changes to GitHub"
echo "2. Connect your repository to Vercel"
echo "3. Configure environment variables in Vercel dashboard"
echo "4. Deploy using: vercel --prod"
echo ""

print_success "Deployment script completed! 🚀"
