'use client';

import { motion } from 'framer-motion';
import { ArrowRight, Download, Github, Linkedin, Mail } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';

interface HeroProps {
  name: string;
  title: string;
  elevatorPitch: string;
  profileImage?: string;
  resumeUrl?: string;
  social?: {
    github?: string;
    linkedin?: string;
    email?: string;
  };
}

export function Hero({
  name,
  title,
  elevatorPitch,
  profileImage,
  resumeUrl,
  social,
}: HeroProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const socialIcons = [
    {
      icon: Github,
      href: social?.github ? `https://github.com/${social.github}` : undefined,
      label: 'GitHub',
    },
    {
      icon: Linkedin,
      href: social?.linkedin ? `https://linkedin.com/in/${social.linkedin}` : undefined,
      label: 'LinkedIn',
    },
    {
      icon: Mail,
      href: social?.email ? `mailto:${social.email}` : undefined,
      label: 'Email',
    },
  ];

  return (
    <section className="relative min-h-[90vh] flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="container mx-auto px-4 py-20">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
        >
          {/* Content */}
          <div className="space-y-8">
            <motion.div variants={ANIMATION_VARIANTS.slideUp} className="space-y-4">
              <Typography variant="large" className="text-primary font-medium">
                Hello, I'm
              </Typography>
              <Typography variant="h1" className="text-4xl lg:text-6xl font-bold">
                {name}
              </Typography>
              <Typography variant="h2" className="text-2xl lg:text-3xl text-muted-foreground font-normal border-none pb-0">
                {title}
              </Typography>
            </motion.div>

            <motion.div variants={ANIMATION_VARIANTS.slideUp}>
              <Typography variant="lead" className="text-lg leading-relaxed max-w-2xl">
                {elevatorPitch}
              </Typography>
            </motion.div>

            {/* Call to Action Buttons */}
            <motion.div
              variants={ANIMATION_VARIANTS.slideUp}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button href="/contact" size="lg" className="group">
                Get in Touch
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
              {resumeUrl && (
                <Button href={resumeUrl} variant="outline" size="lg" external>
                  <Download className="mr-2 h-4 w-4" />
                  Download Resume
                </Button>
              )}
            </motion.div>

            {/* Social Links */}
            <motion.div
              variants={ANIMATION_VARIANTS.slideUp}
              className="flex items-center space-x-4 pt-4"
            >
              <Typography variant="small" className="text-muted-foreground">
                Connect with me:
              </Typography>
              <div className="flex space-x-2">
                {socialIcons.map(({ icon: Icon, href, label }) => (
                  href && (
                    <Button
                      key={label}
                      href={href}
                      variant="ghost"
                      size="icon"
                      external
                      className="hover:text-primary transition-colors"
                      aria-label={label}
                    >
                      <Icon className="h-5 w-5" />
                    </Button>
                  )
                ))}
              </div>
            </motion.div>
          </div>

          {/* Profile Image */}
          <motion.div
            variants={ANIMATION_VARIANTS.slideLeft}
            className="flex justify-center lg:justify-end"
          >
            <div className="relative">
              <motion.div
                className="w-80 h-80 lg:w-96 lg:h-96 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 p-2"
                animate={{
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 20,
                  repeat: Infinity,
                  ease: "linear",
                }}
              >
                <div className="w-full h-full rounded-full bg-background flex items-center justify-center overflow-hidden">
                  {profileImage ? (
                    <img
                      src={profileImage}
                      alt={`${name} - Profile`}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary/10 to-accent/10 rounded-full flex items-center justify-center">
                      <Typography variant="h1" className="text-6xl text-muted-foreground">
                        {name.split(' ').map(n => n[0]).join('')}
                      </Typography>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                className="absolute -top-4 -right-4 w-8 h-8 bg-primary rounded-full"
                animate={{
                  y: [-10, 10, -10],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              <motion.div
                className="absolute -bottom-4 -left-4 w-6 h-6 bg-accent rounded-full"
                animate={{
                  y: [10, -10, 10],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1,
                }}
              />
            </div>
          </motion.div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          variants={ANIMATION_VARIANTS.fadeIn}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{
              y: [0, 10, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className="flex flex-col items-center space-y-2 text-muted-foreground"
          >
            <Typography variant="small">Scroll to explore</Typography>
            <div className="w-px h-8 bg-current opacity-50" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
