import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'

// Mock providers if needed
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Common test data
export const mockPersonalData = {
  name: 'Pre<PERSON> Katuwal',
  title: 'AI Researcher & Software Engineer',
  email: '<EMAIL>',
  location: 'Chengdu, China',
  bio: 'Master\'s student in Computer Science (AI) at UESTC',
  social: {
    github: 'Katwal-77',
    linkedin: 'premkatuwal',
    twitter: 'premkatuwal',
  },
}

export const mockProject = {
  id: 'test-project',
  title: 'Test Project',
  description: 'A test project description',
  technologies: ['React', 'TypeScript'],
  category: 'web',
  status: 'completed',
  featured: true,
  image: '/test-image.jpg',
  demoUrl: 'https://demo.example.com',
  githubUrl: 'https://github.com/test/project',
}

export const mockBlogPost = {
  id: 'test-post',
  slug: 'test-post',
  title: 'Test Blog Post',
  excerpt: 'This is a test blog post excerpt',
  content: 'This is the full content of the test blog post',
  publishedAt: '2024-01-01',
  tags: ['test', 'blog'],
  category: 'technology',
  readingTime: 5,
  featured: false,
  author: 'Prem Katuwal',
}

export const mockTestimonial = {
  id: 'test-testimonial',
  name: 'John Doe',
  role: 'Software Engineer',
  organization: 'Tech Company',
  content: 'Great work and collaboration!',
  rating: 5,
  category: 'professional',
  featured: true,
  date: '2024-01-01',
  image: '/test-avatar.jpg',
}

// Accessibility testing helpers
export const axeMatchers = {
  toHaveNoViolations: expect.extend({
    toHaveNoViolations(received) {
      const violations = received.violations || []
      const pass = violations.length === 0

      if (pass) {
        return {
          message: () => 'Expected accessibility violations, but none were found',
          pass: true,
        }
      } else {
        const violationMessages = violations.map((violation: any) => 
          `${violation.id}: ${violation.description}`
        ).join('\n')

        return {
          message: () => `Expected no accessibility violations, but found:\n${violationMessages}`,
          pass: false,
        }
      }
    },
  }),
}

// Performance testing helpers
export const measurePerformance = (fn: () => void) => {
  const start = performance.now()
  fn()
  const end = performance.now()
  return end - start
}

// Mock data generators
export const generateMockProjects = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    ...mockProject,
    id: `project-${i}`,
    title: `Project ${i + 1}`,
  }))
}

export const generateMockBlogPosts = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    ...mockBlogPost,
    id: `post-${i}`,
    slug: `post-${i}`,
    title: `Blog Post ${i + 1}`,
  }))
}
