'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, X, BookOpen, TrendingUp, Clock } from 'lucide-react';
import { Typography } from '@/components/ui/Typography';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { BlogCard } from '@/components/features/blog/BlogCard';
import { ANIMATION_VARIANTS, BLOG_CATEGORIES } from '@/lib/constants';
import type { BlogPost } from '@/types';

interface BlogPageClientProps {
  allPosts: BlogPost[];
  categories: string[];
  tags: string[];
  featuredPosts: BlogPost[];
  stats: {
    totalPosts: number;
    totalCategories: number;
    totalTags: number;
    featuredPosts: number;
    averageReadingTime: number;
  };
}

export function BlogPageClient({ allPosts, categories, tags, featuredPosts, stats }: BlogPageClientProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Filter posts based on search and filters
  const filteredPosts = useMemo(() => {
    return allPosts.filter((post: BlogPost) => {
      // Search query filter
      if (searchQuery) {
        const searchTerm = searchQuery.toLowerCase();
        const matchesTitle = post.title.toLowerCase().includes(searchTerm);
        const matchesExcerpt = post.excerpt.toLowerCase().includes(searchTerm);
        const matchesTags = post.tags.some(tag => tag.toLowerCase().includes(searchTerm));
        
        if (!matchesTitle && !matchesExcerpt && !matchesTags) {
          return false;
        }
      }

      // Category filter
      if (selectedCategory && post.category !== selectedCategory) {
        return false;
      }

      // Tags filter
      if (selectedTags.length > 0) {
        const hasMatchingTag = selectedTags.some(tag => post.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    });
  }, [allPosts, searchQuery, selectedCategory, selectedTags]);

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('');
    setSelectedTags([]);
  };

  const hasActiveFilters = searchQuery || selectedCategory || selectedTags.length > 0;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          Blog & Insights
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          Thoughts on AI research, software engineering, career transitions, and the intersection 
          of technology and academia. Join me on this journey of continuous learning and discovery.
        </Typography>
      </motion.div>

      {/* Blog Statistics */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
      >
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <BookOpen className="h-8 w-8 text-primary" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.totalPosts}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Total Posts
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <TrendingUp className="h-8 w-8 text-accent" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.featuredPosts}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Featured Posts
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Filter className="h-8 w-8 text-primary" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.totalCategories}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Categories
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Clock className="h-8 w-8 text-accent" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {stats.averageReadingTime}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Avg. Read Time
          </Typography>
        </div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="space-y-4"
      >
        {/* Search Bar */}
        <div className="relative max-w-2xl mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search posts by title, content, or tags..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4"
          />
        </div>

        {/* Filter Controls */}
        <div className="flex items-center justify-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="bg-primary text-primary-foreground rounded-full w-5 h-5 text-xs flex items-center justify-center">
                {(selectedCategory ? 1 : 0) + selectedTags.length}
              </span>
            )}
          </Button>

          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear Filters
            </Button>
          )}

          <Typography variant="small" className="text-muted-foreground">
            {filteredPosts.length} post{filteredPosts.length !== 1 ? 's' : ''}
          </Typography>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="max-w-4xl mx-auto p-4 bg-muted/50 rounded-lg border space-y-4">
            {/* Category Filter */}
            <div className="space-y-2">
              <Typography variant="small" className="font-medium">Categories</Typography>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(selectedCategory === category ? '' : category)}
                  >
                    {BLOG_CATEGORIES[category as keyof typeof BLOG_CATEGORIES] || category}
                  </Button>
                ))}
              </div>
            </div>

            {/* Tags Filter */}
            <div className="space-y-2">
              <Typography variant="small" className="font-medium">Tags</Typography>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {tags.slice(0, 20).map((tag) => (
                  <Button
                    key={tag}
                    variant={selectedTags.includes(tag) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleTag(tag)}
                  >
                    {tag}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Featured Posts */}
      {!hasActiveFilters && featuredPosts.length > 0 && (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          className="space-y-8"
        >
          <Typography variant="h2" className="text-2xl font-bold text-center">
            Featured Posts
          </Typography>
          <div className="grid grid-cols-1 gap-8">
            {featuredPosts.slice(0, 2).map((post) => (
              <BlogCard
                key={post.id}
                post={post}
                variant="featured"
              />
            ))}
          </div>
        </motion.div>
      )}

      {/* All Posts */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-8"
      >
        {!hasActiveFilters && (
          <Typography variant="h2" className="text-2xl font-bold text-center">
            All Posts
          </Typography>
        )}
        
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post, index) => (
              <BlogCard
                key={post.id}
                post={post}
                index={index}
                variant="default"
              />
            ))}
          </div>
        ) : (
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="text-center py-12"
          >
            <Typography variant="h3" className="text-muted-foreground mb-4">
              No posts found
            </Typography>
            <Typography variant="p" className="text-muted-foreground">
              Try adjusting your search criteria or filters to find relevant posts.
            </Typography>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}
