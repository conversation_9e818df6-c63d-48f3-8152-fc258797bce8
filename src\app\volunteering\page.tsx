'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Heart, Users, Clock, Award } from 'lucide-react';
import { Typography } from '@/components/ui/Typography';
import { Button } from '@/components/ui/Button';
import { VolunteeringCard } from '@/components/features/volunteering/VolunteeringCard';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import volunteeringData from '@/data/volunteering.json';
import type { VolunteeringExperience } from '@/types';

export default function VolunteeringPage() {
  const [filter, setFilter] = useState<'all' | 'current' | 'past'>('all');

  // Filter volunteering experiences
  const filteredExperiences = useMemo(() => {
    return volunteeringData.filter((experience: VolunteeringExperience) => {
      if (filter === 'current') return experience.current;
      if (filter === 'past') return !experience.current;
      return true;
    });
  }, [filter]);

  // Sort experiences: current first, then by start date (newest first)
  const sortedExperiences = useMemo(() => {
    return [...filteredExperiences].sort((a, b) => {
      // Current experiences first
      if (a.current && !b.current) return -1;
      if (!a.current && b.current) return 1;
      
      // Then by start date (newest first)
      return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
    });
  }, [filteredExperiences]);

  // Calculate total volunteering years
  const totalYears = useMemo(() => {
    const allYears = volunteeringData.map(exp => {
      const start = new Date(exp.startDate).getFullYear();
      const end = exp.endDate ? new Date(exp.endDate).getFullYear() : new Date().getFullYear();
      return { start, end };
    });
    
    const uniqueYears = new Set();
    allYears.forEach(({ start, end }) => {
      for (let year = start; year <= end; year++) {
        uniqueYears.add(year);
      }
    });
    
    return uniqueYears.size;
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          Volunteering & Community Impact
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          Over {totalYears} years of dedicated community service, from UN field work to local tech education. 
          Each experience has shaped my commitment to using technology and education to create positive change.
        </Typography>
      </motion.div>

      {/* Filter Buttons */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="flex justify-center space-x-4"
      >
        <Button
          variant={filter === 'all' ? 'default' : 'outline'}
          onClick={() => setFilter('all')}
        >
          All Experiences ({volunteeringData.length})
        </Button>
        <Button
          variant={filter === 'current' ? 'default' : 'outline'}
          onClick={() => setFilter('current')}
        >
          Current ({volunteeringData.filter(exp => exp.current).length})
        </Button>
        <Button
          variant={filter === 'past' ? 'default' : 'outline'}
          onClick={() => setFilter('past')}
        >
          Past ({volunteeringData.filter(exp => !exp.current).length})
        </Button>
      </motion.div>

      {/* Volunteering Experiences Grid */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {sortedExperiences.length > 0 ? (
          sortedExperiences.map((experience, index) => (
            <VolunteeringCard
              key={experience.id}
              experience={experience}
              index={index}
            />
          ))
        ) : (
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="col-span-full text-center py-12"
          >
            <Typography variant="h3" className="text-muted-foreground mb-4">
              No experiences found
            </Typography>
            <Typography variant="p" className="text-muted-foreground">
              Try adjusting your filter to see relevant experiences.
            </Typography>
          </motion.div>
        )}
      </motion.div>

      {/* Impact Statistics */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-12 border-t"
      >
        <Typography variant="h3" className="text-2xl font-bold mb-8">
          Community Impact Overview
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div className="space-y-2">
            <div className="flex justify-center">
              <Clock className="h-8 w-8 text-primary" />
            </div>
            <Typography variant="h3" className="text-2xl font-bold text-primary">
              {totalYears}+
            </Typography>
            <Typography variant="small" className="text-muted-foreground">
              Years of Service
            </Typography>
          </div>
          <div className="space-y-2">
            <div className="flex justify-center">
              <Heart className="h-8 w-8 text-accent" />
            </div>
            <Typography variant="h3" className="text-2xl font-bold text-primary">
              {volunteeringData.length}
            </Typography>
            <Typography variant="small" className="text-muted-foreground">
              Organizations Served
            </Typography>
          </div>
          <div className="space-y-2">
            <div className="flex justify-center">
              <Users className="h-8 w-8 text-primary" />
            </div>
            <Typography variant="h3" className="text-2xl font-bold text-primary">
              1000+
            </Typography>
            <Typography variant="small" className="text-muted-foreground">
              People Impacted
            </Typography>
          </div>
          <div className="space-y-2">
            <div className="flex justify-center">
              <Award className="h-8 w-8 text-accent" />
            </div>
            <Typography variant="h3" className="text-2xl font-bold text-primary">
              {volunteeringData.filter(exp => exp.current).length}
            </Typography>
            <Typography variant="small" className="text-muted-foreground">
              Active Commitments
            </Typography>
          </div>
        </div>
      </motion.div>

      {/* Personal Reflection */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8"
      >
        <div className="max-w-4xl mx-auto p-8 bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg border">
          <Typography variant="h4" className="mb-6">
            Why Volunteering Matters to Me
          </Typography>
          <Typography variant="p" className="text-muted-foreground mb-6 leading-relaxed">
            My volunteering journey began with a simple belief: technology and education can be powerful 
            forces for positive change. From conducting UN field surveys to teaching coding to seniors, 
            each experience has reinforced my commitment to using my skills to benefit others. 
            These experiences have not only allowed me to give back to the community but have also 
            shaped me as a researcher, developer, and human being.
          </Typography>
          <Typography variant="p" className="text-muted-foreground leading-relaxed">
            Whether it's helping a nonprofit modernize their systems, mentoring the next generation 
            of engineers, or contributing to open-source projects that benefit thousands of developers, 
            I believe that our individual contributions, no matter how small, can create ripple effects 
            that transform communities and lives.
          </Typography>
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8"
      >
        <div className="max-w-2xl mx-auto p-6 bg-muted/50 rounded-lg border">
          <Typography variant="h4" className="mb-4">
            Interested in Collaboration?
          </Typography>
          <Typography variant="p" className="text-muted-foreground mb-6">
            I'm always looking for meaningful ways to contribute to the community. 
            If you have a project, organization, or initiative that could benefit from 
            my technical skills or experience, I'd love to hear about it.
          </Typography>
          <Button href="/contact" size="lg">
            Let's Make an Impact Together
          </Button>
        </div>
      </motion.div>
    </div>
  );
}
