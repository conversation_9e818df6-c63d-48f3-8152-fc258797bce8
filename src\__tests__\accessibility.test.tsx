import { render } from '@/__tests__/utils/test-utils'
import { axe, toHaveNoViolations } from 'jest-axe'
import { Typography } from '@/components/ui/Typography'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card'

// Extend Jest matchers
expect.extend(toHaveNoViolations)

describe('Accessibility Tests', () => {
  describe('Typography Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <div>
          <Typography variant="h1">Main Heading</Typography>
          <Typography variant="h2">Section Heading</Typography>
          <Typography variant="p">This is a paragraph with some content.</Typography>
          <Typography variant="small">Small text content</Typography>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper heading hierarchy', async () => {
      const { container } = render(
        <div>
          <Typography variant="h1">Page Title</Typography>
          <Typography variant="h2">Section Title</Typography>
          <Typography variant="h3">Subsection Title</Typography>
          <Typography variant="p">Content paragraph</Typography>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should handle links properly', async () => {
      const { container } = render(
        <div>
          <Typography href="/internal">Internal Link</Typography>
          <Typography href="https://external.com" external>
            External Link
          </Typography>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Button Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <div>
          <Button>Default Button</Button>
          <Button variant="outline">Outline Button</Button>
          <Button variant="ghost">Ghost Button</Button>
          <Button disabled>Disabled Button</Button>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should handle button links properly', async () => {
      const { container } = render(
        <div>
          <Button href="/internal">Internal Link Button</Button>
          <Button href="https://external.com" external>
            External Link Button
          </Button>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper focus management', async () => {
      const { container } = render(
        <div>
          <Button>Focusable Button</Button>
          <Button disabled>Disabled Button</Button>
          <Button tabIndex={-1}>Non-focusable Button</Button>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Card Component', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <Card>
          <CardHeader>
            <CardTitle>Card Title</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This is the card content with some meaningful text.</p>
          </CardContent>
        </Card>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should handle interactive cards properly', async () => {
      const { container } = render(
        <Card>
          <CardHeader>
            <CardTitle>Interactive Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Card with interactive elements</p>
            <Button>Action Button</Button>
            <Typography href="/link">Card Link</Typography>
          </CardContent>
        </Card>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Form Elements', () => {
    it('should handle form accessibility correctly', async () => {
      const { container } = render(
        <form>
          <div>
            <label htmlFor="name">Name</label>
            <input id="name" type="text" required aria-describedby="name-help" />
            <div id="name-help">Enter your full name</div>
          </div>
          <div>
            <label htmlFor="email">Email</label>
            <input id="email" type="email" required aria-describedby="email-help" />
            <div id="email-help">Enter a valid email address</div>
          </div>
          <Button type="submit">Submit</Button>
        </form>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should handle error states properly', async () => {
      const { container } = render(
        <form>
          <div>
            <label htmlFor="invalid-field">Required Field</label>
            <input 
              id="invalid-field" 
              type="text" 
              required 
              aria-invalid="true"
              aria-describedby="field-error"
            />
            <div id="field-error" role="alert">
              This field is required
            </div>
          </div>
        </form>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Navigation Elements', () => {
    it('should handle navigation accessibility', async () => {
      const { container } = render(
        <nav aria-label="Main navigation">
          <ul>
            <li><Typography href="/">Home</Typography></li>
            <li><Typography href="/about">About</Typography></li>
            <li><Typography href="/projects">Projects</Typography></li>
            <li><Typography href="/contact">Contact</Typography></li>
          </ul>
        </nav>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should handle breadcrumb navigation', async () => {
      const { container } = render(
        <nav aria-label="Breadcrumb">
          <ol>
            <li><Typography href="/">Home</Typography></li>
            <li><Typography href="/projects">Projects</Typography></li>
            <li aria-current="page">Current Page</li>
          </ol>
        </nav>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Image and Media', () => {
    it('should handle images with proper alt text', async () => {
      const { container } = render(
        <div>
          <img src="/test-image.jpg" alt="Descriptive alt text for the image" />
          <img src="/decorative-image.jpg" alt="" role="presentation" />
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Color Contrast and Visual Elements', () => {
    it('should not have color contrast violations', async () => {
      const { container } = render(
        <div>
          <Typography className="text-foreground bg-background p-4">
            Normal text with proper contrast
          </Typography>
          <Typography className="text-muted-foreground bg-background p-4">
            Muted text with proper contrast
          </Typography>
          <Button variant="default">Primary Button</Button>
          <Button variant="outline">Outline Button</Button>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('ARIA Labels and Descriptions', () => {
    it('should handle ARIA attributes correctly', async () => {
      const { container } = render(
        <div>
          <Button aria-label="Close dialog">×</Button>
          <div role="status" aria-live="polite">
            Status message
          </div>
          <div role="alert" aria-live="assertive">
            Error message
          </div>
          <Button aria-describedby="button-help">
            Action Button
          </Button>
          <div id="button-help">
            This button performs an important action
          </div>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })
})
