'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Typography } from '@/components/ui/Typography';
import { ProjectCard } from '@/components/features/projects/ProjectCard';
import { ProjectFilter } from '@/components/features/projects/ProjectFilter';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import projectsData from '@/data/projects.json';
import type { Project } from '@/types';

export default function ProjectsPage() {
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    technologies: [] as string[],
    featured: false,
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Extract unique technologies for filter
  const availableTechnologies = useMemo(() => {
    const allTechnologies = projectsData.flatMap(project => project.technologies);
    return Array.from(new Set(allTechnologies)).sort();
  }, []);

  // Filter projects
  const filteredProjects = useMemo(() => {
    return projectsData.filter((project: Project) => {
      // Category filter
      if (filters.category && project.category !== filters.category) {
        return false;
      }

      // Status filter
      if (filters.status && project.status !== filters.status) {
        return false;
      }

      // Featured filter
      if (filters.featured && !project.featured) {
        return false;
      }

      // Technology filter
      if (filters.technologies.length > 0) {
        const hasMatchingTech = filters.technologies.some(tech => 
          project.technologies.includes(tech)
        );
        if (!hasMatchingTech) {
          return false;
        }
      }

      return true;
    });
  }, [filters]);

  // Sort projects: featured first, then by start date (newest first)
  const sortedProjects = useMemo(() => {
    return [...filteredProjects].sort((a, b) => {
      // Featured projects first
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;
      
      // Then by start date (newest first)
      return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
    });
  }, [filteredProjects]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          Projects Portfolio
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          Explore my technical projects spanning AI research, web development, 
          and open-source contributions. Each project represents a unique challenge 
          and innovative solution in the world of technology.
        </Typography>
      </motion.div>

      {/* Filter Component */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
      >
        <ProjectFilter
          onFilterChange={setFilters}
          totalResults={sortedProjects.length}
          availableTechnologies={availableTechnologies}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
        />
      </motion.div>

      {/* Projects Grid/List */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
            : 'space-y-6'
        }
      >
        {sortedProjects.length > 0 ? (
          sortedProjects.map((project, index) => (
            <ProjectCard
              key={project.id}
              project={project}
              index={index}
            />
          ))
        ) : (
          <motion.div
            variants={ANIMATION_VARIANTS.slideUp}
            className="col-span-full text-center py-12"
          >
            <Typography variant="h3" className="text-muted-foreground mb-4">
              No projects found
            </Typography>
            <Typography variant="p" className="text-muted-foreground">
              Try adjusting your filters to find relevant projects.
            </Typography>
          </motion.div>
        )}
      </motion.div>

      {/* Project Statistics */}
      {sortedProjects.length > 0 && (
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={ANIMATION_VARIANTS.slideUp}
          className="text-center pt-12 border-t"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {projectsData.length}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Total Projects
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {projectsData.filter(p => p.featured).length}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Featured Projects
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {availableTechnologies.length}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Technologies Used
              </Typography>
            </div>
            <div className="space-y-2">
              <Typography variant="h3" className="text-2xl font-bold text-primary">
                {projectsData.filter(p => p.status === 'completed').length}
              </Typography>
              <Typography variant="small" className="text-muted-foreground">
                Completed Projects
              </Typography>
            </div>
          </div>
        </motion.div>
      )}

      {/* Call to Action */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8"
      >
        <div className="max-w-2xl mx-auto p-6 bg-muted/50 rounded-lg border">
          <Typography variant="h4" className="mb-4">
            Interested in Collaboration?
          </Typography>
          <Typography variant="p" className="text-muted-foreground mb-6">
            I'm always excited to work on innovative projects and explore new technologies. 
            Whether you have an idea for collaboration or want to discuss any of these projects, 
            I'd love to hear from you.
          </Typography>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-md font-medium hover:bg-primary/90 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Get in Touch
            </motion.a>
            <motion.a
              href="https://github.com/yourusername"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-6 py-3 border border-input bg-background rounded-md font-medium hover:bg-accent hover:text-accent-foreground transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              View GitHub
            </motion.a>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
