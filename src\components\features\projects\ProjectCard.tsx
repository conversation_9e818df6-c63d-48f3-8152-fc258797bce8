'use client';

import { motion } from 'framer-motion';
import { ExternalLink, Github, Calendar, Star, TrendingUp } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS, PROJECT_CATEGORIES } from '@/lib/constants';
import type { Project } from '@/types';

interface ProjectCardProps {
  project: Project;
  index?: number;
}

export function ProjectCard({ project, index = 0 }: ProjectCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ai-ml':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'web-dev':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'research-tools':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'open-source':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'planned':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  };

  const getProjectDuration = () => {
    const start = new Date(project.startDate);
    const end = project.endDate ? new Date(project.endDate) : new Date();
    const months = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 30));
    return months > 12 ? `${Math.round(months / 12)} year${Math.round(months / 12) > 1 ? 's' : ''}` : `${months} month${months > 1 ? 's' : ''}`;
  };

  return (
    <motion.div
      variants={ANIMATION_VARIANTS.slideUp}
      whileHover={{ y: -8 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 group overflow-hidden">
        {/* Project Image */}
        {project.imageUrl && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={project.imageUrl}
              alt={`${project.title} screenshot`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            
            {/* Status Badge */}
            <div className="absolute top-4 right-4">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                  project.status
                )}`}
              >
                {project.status === 'in-progress' && '🚧 '}
                {project.status === 'completed' && '✅ '}
                {project.status === 'planned' && '📋 '}
                {project.status.charAt(0).toUpperCase() + project.status.slice(1).replace('-', ' ')}
              </span>
            </div>
          </div>
        )}

        <CardHeader className="space-y-4">
          {/* Header with badges */}
          <div className="flex items-center justify-between flex-wrap gap-2">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(
                project.category
              )}`}
            >
              {PROJECT_CATEGORIES[project.category as keyof typeof PROJECT_CATEGORIES]}
            </span>
            <div className="flex items-center space-x-2">
              {project.featured && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </span>
              )}
            </div>
          </div>

          {/* Title and Description */}
          <div className="space-y-2">
            <CardTitle className="text-xl group-hover:text-primary transition-colors leading-tight">
              {project.title}
            </CardTitle>
            <CardDescription className="text-sm line-clamp-3">
              {project.description}
            </CardDescription>
          </div>

          {/* Project Duration */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>
              {formatDate(project.startDate)} - {project.endDate ? formatDate(project.endDate) : 'Present'}
            </span>
            <span className="text-xs">({getProjectDuration()})</span>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Technology Stack */}
          <div className="space-y-2">
            <Typography variant="small" className="font-medium">Tech Stack</Typography>
            <div className="flex flex-wrap gap-1">
              {project.technologies.slice(0, 6).map((tech) => (
                <span
                  key={tech}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground hover:bg-primary hover:text-primary-foreground transition-colors"
                >
                  {tech}
                </span>
              ))}
              {project.technologies.length > 6 && (
                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground">
                  +{project.technologies.length - 6} more
                </span>
              )}
            </div>
          </div>

          {/* Key Highlights */}
          {project.highlights && project.highlights.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-accent" />
                <Typography variant="small" className="font-medium">Key Highlights</Typography>
              </div>
              <ul className="space-y-1">
                {project.highlights.slice(0, 2).map((highlight, idx) => (
                  <li key={idx} className="text-xs text-muted-foreground flex items-start">
                    <span className="text-accent mr-2">•</span>
                    <span>{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center space-x-2 pt-4">
            {project.demoUrl && (
              <Button
                href={project.demoUrl}
                variant="default"
                size="sm"
                external
                className="flex-1 group/btn"
              >
                View Demo
                <ExternalLink className="ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-0.5" />
              </Button>
            )}
            {project.githubUrl && (
              <Button
                href={project.githubUrl}
                variant="outline"
                size="sm"
                external
                className="group/btn"
              >
                <Github className="h-3 w-3 transition-transform group-hover/btn:scale-110" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
