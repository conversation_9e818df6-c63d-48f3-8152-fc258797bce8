// Application constants

export const SITE_CONFIG = {
  name: '<PERSON><PERSON>',
  description: 'Master\'s student in Computer Science (AI) at UESTC, ranked 3rd globally in AI, showcasing research, projects, and professional experience',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  author: '<PERSON><PERSON>',
  keywords: [
    'Computer Science',
    'Master\'s Student',
    'AI Research',
    'UESTC',
    'Machine Learning',
    'Software Development',
    'Publications',
    'Portfolio',
    'Artificial Intelligence',
    'Chengdu',
    'China',
  ],
} as const;

export const NAVIGATION_ITEMS = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Journey', href: '/journey' },
  { name: 'Research', href: '/research' },
  { name: 'Projects', href: '/projects' },
  { name: 'Volunteering', href: '/volunteering' },
  { name: 'Blog', href: '/blog' },
  { name: 'Testimonials', href: '/testimonials' },
  { name: 'Contact', href: '/contact' },
] as const;

export const SOCIAL_LINKS = {
  github: 'https://github.com',
  linkedin: 'https://linkedin.com/in',
  twitter: 'https://twitter.com',
  scholar: 'https://scholar.google.com/citations?user=',
  orcid: 'https://orcid.org/',
} as const;

export const PROJECT_CATEGORIES = {
  'ai-ml': 'AI & Machine Learning',
  'web-dev': 'Web Development',
  'research-tools': 'Research Tools',
  'open-source': 'Open Source',
} as const;

export const PUBLICATION_TYPES = {
  journal: 'Journal Article',
  conference: 'Conference Paper',
  preprint: 'Preprint',
  thesis: 'Thesis',
} as const;

export const BLOG_CATEGORIES = {
  research: 'Research',
  technology: 'Technology',
  career: 'Career',
  tutorials: 'Tutorials',
} as const;

export const TIMELINE_TYPES = {
  education: 'Education',
  work: 'Work Experience',
  research: 'Research',
  volunteering: 'Volunteering',
} as const;

export const CONTACT_INFO = {
  email: '<EMAIL>',
  location: 'Chengdu, China',
  availability: 'Available for research collaborations and opportunities',
} as const;

export const ANIMATION_VARIANTS = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  },
  slideUp: {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  },
  slideDown: {
    hidden: { opacity: 0, y: -20 },
    visible: { opacity: 1, y: 0 },
  },
  slideLeft: {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0 },
  },
  slideRight: {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
  },
  scale: {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1 },
  },
} as const;

export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export const THEME_COLORS = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a',
  },
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    900: '#0f172a',
  },
} as const;
