'use client';

import { motion } from 'framer-motion';
import { MessageCircle, Users, Lightbulb, Mic } from 'lucide-react';
import { Typography } from '@/components/ui/Typography';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { ContactForm } from '@/components/features/contact/ContactForm';
import { ContactInfo } from '@/components/features/contact/ContactInfo';
import { ResumeSection } from '@/components/features/resume/ResumeDownload';

export function ContactPageClient() {
  const contactReasons = [
    {
      icon: Users,
      title: 'Research Collaboration',
      description: 'Interested in collaborating on AI research, joint publications, or academic projects.',
      examples: ['Joint research projects', 'Co-authoring papers', 'Data sharing initiatives', 'Cross-institutional studies'],
    },
    {
      icon: Lightbulb,
      title: 'Consulting & Advisory',
      description: 'Looking for technical expertise, strategic advice, or project consultation.',
      examples: ['AI strategy consulting', 'Technical architecture review', 'Product development advice', 'Technology assessment'],
    },
    {
      icon: Mic,
      title: 'Speaking & Presentations',
      description: 'Inviting me to speak at conferences, workshops, or educational events.',
      examples: ['Conference presentations', 'Workshop facilitation', 'Guest lectures', 'Panel discussions'],
    },
    {
      icon: MessageCircle,
      title: 'General Inquiries',
      description: 'Questions about my work, career advice, or just wanting to connect.',
      examples: ['Career guidance', 'Academic advice', 'Technology questions', 'Networking'],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const heroVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 25,
        duration: 0.8,
      }
    },
  };

  const sectionVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 25,
      }
    },
  };

  return (
    <div className="container mx-auto px-4 py-16 space-y-20">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={heroVariants}
        className="text-center space-y-8"
      >
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 300, damping: 25 }}
            className="w-24 h-24 bg-gradient-to-br from-primary/20 to-accent/20 rounded-3xl flex items-center justify-center mx-auto"
          >
            <MessageCircle className="h-12 w-12 text-primary" />
          </motion.div>
          <Typography variant="h1" className="text-5xl lg:text-7xl font-bold bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
            Let's Connect
          </Typography>
        </div>
        <Typography variant="lead" className="max-w-4xl mx-auto text-xl lg:text-2xl leading-relaxed">
          I'm always excited to discuss new opportunities, share insights, and connect with
          fellow researchers, developers, and innovators. Whether you have a specific project
          in mind or just want to chat about technology and research, I'd love to hear from you.
        </Typography>
      </motion.div>

      {/* Contact Reasons */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-12"
      >
        <motion.div variants={sectionVariants} className="text-center">
          <Typography variant="h2" className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-4">
            What Can We Discuss?
          </Typography>
          <Typography variant="lead" className="text-muted-foreground max-w-2xl mx-auto">
            I'm passionate about collaboration and always open to meaningful conversations
          </Typography>
        </motion.div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {contactReasons.map((reason) => (
            <motion.div
              key={reason.title}
              variants={sectionVariants}
              whileHover={{ y: -8, scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <Card className="h-full backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-2xl hover:border-primary/20 transition-all duration-500 group overflow-hidden">
                <CardHeader className="pb-6">
                  <div className="flex items-center space-x-4">
                    <motion.div
                      className="w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl flex items-center justify-center group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300"
                      whileHover={{ rotate: 5, scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 20 }}
                    >
                      <reason.icon className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-300" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                        {reason.title}
                      </CardTitle>
                    </div>
                  </div>
                  <CardDescription className="mt-4 text-base leading-relaxed">
                    {reason.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    <Typography variant="small" className="font-semibold text-muted-foreground uppercase tracking-wide">
                      Examples:
                    </Typography>
                    <ul className="space-y-3">
                      {reason.examples.map((example, idx) => (
                        <motion.li
                          key={idx}
                          className="text-sm text-muted-foreground flex items-start group/item"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * idx }}
                        >
                          <span className="text-primary mr-3 mt-1 group-hover/item:scale-125 transition-transform duration-200">•</span>
                          <span className="leading-relaxed">{example}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Main Contact Section */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={containerVariants}
        className="grid grid-cols-1 lg:grid-cols-3 gap-16"
      >
        {/* Contact Form */}
        <motion.div
          variants={sectionVariants}
          className="lg:col-span-2"
        >
          <ContactForm />
        </motion.div>

        {/* Contact Information */}
        <motion.div
          variants={sectionVariants}
          className="lg:col-span-1 space-y-8"
        >
          <ContactInfo />
          <ResumeSection />
        </motion.div>
      </motion.div>

      {/* FAQ Section */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={containerVariants}
        className="space-y-12"
      >
        <motion.div variants={sectionVariants} className="text-center">
          <Typography variant="h2" className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-4">
            Frequently Asked Questions
          </Typography>
          <Typography variant="lead" className="text-muted-foreground max-w-2xl mx-auto">
            Common questions about collaboration, response times, and opportunities
          </Typography>
        </motion.div>
        <div className="max-w-4xl mx-auto space-y-8">
          <motion.div variants={sectionVariants}>
            <Card className="backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-xl hover:border-primary/20 transition-all duration-500">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-foreground">How quickly do you respond to messages?</CardTitle>
              </CardHeader>
              <CardContent>
                <Typography variant="p" className="text-muted-foreground leading-relaxed">
                  I typically respond to all inquiries within 24-48 hours. For urgent matters,
                  please mention it in your subject line, and I'll prioritize your message.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={sectionVariants}>
            <Card className="backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-xl hover:border-primary/20 transition-all duration-500">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-foreground">What types of collaborations are you interested in?</CardTitle>
              </CardHeader>
              <CardContent>
                <Typography variant="p" className="text-muted-foreground leading-relaxed">
                  I'm particularly interested in AI research collaborations, interdisciplinary projects
                  that combine technology with social impact, and opportunities to bridge academic
                  research with practical applications.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={sectionVariants}>
            <Card className="backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-xl hover:border-primary/20 transition-all duration-500">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-foreground">Do you offer mentoring or career advice?</CardTitle>
              </CardHeader>
              <CardContent>
                <Typography variant="p" className="text-muted-foreground leading-relaxed">
                  Yes! I enjoy mentoring students and early-career professionals, especially those
                  interested in AI research, software engineering, or transitioning between industry
                  and academia. Feel free to reach out with specific questions.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={sectionVariants}>
            <Card className="backdrop-blur-sm bg-card/95 border-border/50 hover:shadow-xl hover:border-primary/20 transition-all duration-500">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-foreground">Are you available for speaking engagements?</CardTitle>
              </CardHeader>
              <CardContent>
                <Typography variant="p" className="text-muted-foreground leading-relaxed">
                  I'm open to speaking at conferences, workshops, and educational events, particularly
                  on topics related to AI research, software engineering best practices, and career
                  transitions. Please provide details about your event and timeline.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={heroVariants}
        className="text-center pt-12"
      >
        <motion.div
          className="max-w-3xl mx-auto p-12 bg-gradient-to-br from-primary/10 to-accent/10 rounded-3xl border-2 border-primary/20 backdrop-blur-sm relative overflow-hidden group"
          whileHover={{ scale: 1.02 }}
          transition={{ type: "spring", stiffness: 300, damping: 25 }}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <div className="relative space-y-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 300, damping: 25 }}
              className="w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl flex items-center justify-center mx-auto"
            >
              <MessageCircle className="h-8 w-8 text-primary" />
            </motion.div>
            <Typography variant="h3" className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Ready to Start a Conversation?
            </Typography>
            <Typography variant="p" className="text-muted-foreground text-lg leading-relaxed">
              Whether you have a specific project in mind, want to explore collaboration opportunities,
              or simply want to connect and share ideas, I'm here to listen and engage.
              Let's create something meaningful together.
            </Typography>
            <Typography variant="small" className="text-muted-foreground font-medium">
              I look forward to hearing from you and learning about your projects, ideas, and goals.
            </Typography>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
