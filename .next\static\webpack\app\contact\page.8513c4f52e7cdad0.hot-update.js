"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: () => (/* binding */ PopChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* __next_internal_client_entry_do_not_use__ PopChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */ class PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const parent = element.offsetParent;\n            const parentWidth = (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(parent) ? parent.offsetWidth || 0 : 0;\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n            size.right = parentWidth - size.width - size.left;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */ componentDidUpdate() {}\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild(param) {\n    let { children, isPresent, anchorX } = param;\n    _s();\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        right: 0\n    });\n    const { nonce } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect)({\n        \"PopChild.useInsertionEffect\": ()=>{\n            const { width, height, top, left, right } = size.current;\n            if (isPresent || !ref.current || !width || !height) return;\n            const x = anchorX === \"left\" ? \"left: \".concat(left) : \"right: \".concat(right);\n            ref.current.dataset.motionPopId = id;\n            const style = document.createElement(\"style\");\n            if (nonce) style.nonce = nonce;\n            document.head.appendChild(style);\n            if (style.sheet) {\n                style.sheet.insertRule('\\n          [data-motion-pop-id=\"'.concat(id, '\"] {\\n            position: absolute !important;\\n            width: ').concat(width, \"px !important;\\n            height: \").concat(height, \"px !important;\\n            \").concat(x, \"px !important;\\n            top: \").concat(top, \"px !important;\\n          }\\n        \"));\n            }\n            return ({\n                \"PopChild.useInsertionEffect\": ()=>{\n                    if (document.head.contains(style)) {\n                        document.head.removeChild(style);\n                    }\n                }\n            })[\"PopChild.useInsertionEffect\"];\n        }\n    }[\"PopChild.useInsertionEffect\"], [\n        isPresent\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PopChildMeasure, {\n        isPresent: isPresent,\n        childRef: ref,\n        sizeRef: size,\n        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n            ref\n        })\n    });\n}\n_s(PopChild, \"V7z789Ed2n0+HnmYCJ8kEL0I644=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_1__.useId,\n        react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect\n    ];\n});\n_c = PopChild;\n\nvar _c;\n$RefreshReg$(_c, \"PopChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: () => (/* binding */ PresenceChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n/* __next_internal_client_entry_do_not_use__ PresenceChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\nconst PresenceChild = (param)=>{\n    let { children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, anchorX } = param;\n    _s();\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    let isReusedContext = true;\n    let context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo[context]\": ()=>{\n            isReusedContext = false;\n            return {\n                id,\n                initial,\n                isPresent,\n                custom,\n                onExitComplete: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, true);\n                        for (const isComplete of presenceChildren.values()){\n                            if (!isComplete) return; // can stop searching when any is incomplete\n                        }\n                        onExitComplete && onExitComplete();\n                    }\n                })[\"PresenceChild.useMemo[context]\"],\n                register: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, false);\n                        return ({\n                            \"PresenceChild.useMemo[context]\": ()=>presenceChildren.delete(childId)\n                        })[\"PresenceChild.useMemo[context]\"];\n                    }\n                })[\"PresenceChild.useMemo[context]\"]\n            };\n        }\n    }[\"PresenceChild.useMemo[context]\"], [\n        isPresent,\n        presenceChildren,\n        onExitComplete\n    ]);\n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */ if (presenceAffectsLayout && isReusedContext) {\n        context = {\n            ...context\n        };\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo\": ()=>{\n            presenceChildren.forEach({\n                \"PresenceChild.useMemo\": (_, key)=>presenceChildren.set(key, false)\n            }[\"PresenceChild.useMemo\"]);\n        }\n    }[\"PresenceChild.useMemo\"], [\n        isPresent\n    ]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */ react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PresenceChild.useEffect\": ()=>{\n            !isPresent && !presenceChildren.size && onExitComplete && onExitComplete();\n        }\n    }[\"PresenceChild.useEffect\"], [\n        isPresent\n    ]);\n    if (mode === \"popLayout\") {\n        children = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__.PopChild, {\n            isPresent: isPresent,\n            anchorX: anchorX,\n            children: children\n        });\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__.PresenceContext.Provider, {\n        value: context,\n        children: children\n    });\n};\n_s(PresenceChild, \"LuJRAK72iQdFH7nv0cJ6ZjdNWv8=\", false, function() {\n    return [\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant,\n        react__WEBPACK_IMPORTED_MODULE_1__.useId\n    ];\n});\n_c = PresenceChild;\nfunction newChildrenMap() {\n    return new Map();\n}\n\nvar _c;\n$RefreshReg$(_c, \"PresenceChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: () => (/* binding */ AnimatePresence)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-presence.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\");\n/* __next_internal_client_entry_do_not_use__ AnimatePresence auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */ const AnimatePresence = (param)=>{\n    let { children, custom, initial = true, onExitComplete, presenceAffectsLayout = true, mode = \"sync\", propagate = false, anchorX = \"left\" } = param;\n    _s();\n    const [isParentPresent, safeToRemove] = (0,_use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence)(propagate);\n    /**\n     * Filter any children that aren't ReactElements. We can only track components\n     * between renders with a props.key.\n     */ const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnimatePresence.useMemo[presentChildren]\": ()=>(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(children)\n    }[\"AnimatePresence.useMemo[presentChildren]\"], [\n        children\n    ]);\n    /**\n     * Track the keys of the currently rendered children. This is used to\n     * determine which children are exiting.\n     */ const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey);\n    /**\n     * If `initial={false}` we only want to pass this to components in the first render.\n     */ const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    /**\n     * A ref containing the currently present children. When all exit animations\n     * are complete, we use this to re-render the component with the latest children\n     * *committed* rather than the latest children *rendered*.\n     */ const pendingPresentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(presentChildren);\n    /**\n     * Track which exiting children have finished animating out.\n     */ const exitComplete = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant)({\n        \"AnimatePresence.useConstant[exitComplete]\": ()=>new Map()\n    }[\"AnimatePresence.useConstant[exitComplete]\"]);\n    /**\n     * Save children to render as React state. To ensure this component is concurrent-safe,\n     * we check for exiting children via an effect.\n     */ const [diffedChildren, setDiffedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    const [renderedChildren, setRenderedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)({\n        \"AnimatePresence.useIsomorphicLayoutEffect\": ()=>{\n            isInitialRender.current = false;\n            pendingPresentChildren.current = presentChildren;\n            /**\n         * Update complete status of exiting children.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n                const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(renderedChildren[i]);\n                if (!presentKeys.includes(key)) {\n                    if (exitComplete.get(key) !== true) {\n                        exitComplete.set(key, false);\n                    }\n                } else {\n                    exitComplete.delete(key);\n                }\n            }\n        }\n    }[\"AnimatePresence.useIsomorphicLayoutEffect\"], [\n        renderedChildren,\n        presentKeys.length,\n        presentKeys.join(\"-\")\n    ]);\n    const exitingChildren = [];\n    if (presentChildren !== diffedChildren) {\n        let nextChildren = [\n            ...presentChildren\n        ];\n        /**\n         * Loop through all the currently rendered components and decide which\n         * are exiting.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n            const child = renderedChildren[i];\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            if (!presentKeys.includes(key)) {\n                nextChildren.splice(i, 0, child);\n                exitingChildren.push(child);\n            }\n        }\n        /**\n         * If we're in \"wait\" mode, and we have exiting children, we want to\n         * only render these until they've all exited.\n         */ if (mode === \"wait\" && exitingChildren.length) {\n            nextChildren = exitingChildren;\n        }\n        setRenderedChildren((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(nextChildren));\n        setDiffedChildren(presentChildren);\n        /**\n         * Early return to ensure once we've set state with the latest diffed\n         * children, we can immediately re-render.\n         */ return null;\n    }\n    if ( true && mode === \"wait\" && renderedChildren.length > 1) {\n        console.warn('You\\'re attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.');\n    }\n    /**\n     * If we've been provided a forceRender function by the LayoutGroupContext,\n     * we can use it to force a re-render amongst all surrounding components once\n     * all components have finished animating out.\n     */ const { forceRender } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.LayoutGroupContext);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: renderedChildren.map((child)=>{\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            const isPresent = propagate && !isParentPresent ? false : presentChildren === renderedChildren || presentKeys.includes(key);\n            const onExit = ()=>{\n                if (exitComplete.has(key)) {\n                    exitComplete.set(key, true);\n                } else {\n                    return;\n                }\n                let isEveryExitComplete = true;\n                exitComplete.forEach((isExitComplete)=>{\n                    if (!isExitComplete) isEveryExitComplete = false;\n                });\n                if (isEveryExitComplete) {\n                    forceRender === null || forceRender === void 0 ? void 0 : forceRender();\n                    setRenderedChildren(pendingPresentChildren.current);\n                    propagate && (safeToRemove === null || safeToRemove === void 0 ? void 0 : safeToRemove());\n                    onExitComplete && onExitComplete();\n                }\n            };\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, {\n                isPresent: isPresent,\n                initial: !isInitialRender.current || initial ? undefined : false,\n                custom: custom,\n                presenceAffectsLayout: presenceAffectsLayout,\n                mode: mode,\n                onExitComplete: isPresent ? undefined : onExit,\n                anchorX: anchorX,\n                children: child\n            }, key);\n        })\n    });\n};\n_s(AnimatePresence, \"hskVsE2zKTQdrb/joPYe18qtIRg=\", false, function() {\n    return [\n        _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence,\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant,\n        _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = AnimatePresence;\n\nvar _c;\n$RefreshReg$(_c, \"AnimatePresence\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChildKey: () => (/* binding */ getChildKey),\n/* harmony export */   onlyElements: () => (/* binding */ onlyElements)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nconst getChildKey = (child)=>child.key || \"\";\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child)=>{\n        if (/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) filtered.push(child);\n    });\n    return filtered;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDtBQUVqRCxNQUFNRSxjQUFjLENBQUNDLFFBQVVBLE1BQU1DLEdBQUcsSUFBSTtBQUM1QyxTQUFTQyxhQUFhQyxRQUFRO0lBQzFCLE1BQU1DLFdBQVcsRUFBRTtJQUNuQiwwRkFBMEY7SUFDMUZQLDJDQUFRQSxDQUFDUSxPQUFPLENBQUNGLFVBQVUsQ0FBQ0g7UUFDeEIsa0JBQUlGLHFEQUFjQSxDQUFDRSxRQUNmSSxTQUFTRSxJQUFJLENBQUNOO0lBQ3RCO0lBQ0EsT0FBT0k7QUFDWDtBQUVxQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwcmVta1xcRGVza3RvcFxccGZcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXGNvbXBvbmVudHNcXEFuaW1hdGVQcmVzZW5jZVxcdXRpbHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENoaWxkcmVuLCBpc1ZhbGlkRWxlbWVudCB9IGZyb20gJ3JlYWN0JztcblxuY29uc3QgZ2V0Q2hpbGRLZXkgPSAoY2hpbGQpID0+IGNoaWxkLmtleSB8fCBcIlwiO1xuZnVuY3Rpb24gb25seUVsZW1lbnRzKGNoaWxkcmVuKSB7XG4gICAgY29uc3QgZmlsdGVyZWQgPSBbXTtcbiAgICAvLyBXZSB1c2UgZm9yRWFjaCBoZXJlIGluc3RlYWQgb2YgbWFwIGFzIG1hcCBtdXRhdGVzIHRoZSBjb21wb25lbnQga2V5IGJ5IHByZXByZW5kaW5nIGAuJGBcbiAgICBDaGlsZHJlbi5mb3JFYWNoKGNoaWxkcmVuLCAoY2hpbGQpID0+IHtcbiAgICAgICAgaWYgKGlzVmFsaWRFbGVtZW50KGNoaWxkKSlcbiAgICAgICAgICAgIGZpbHRlcmVkLnB1c2goY2hpbGQpO1xuICAgIH0pO1xuICAgIHJldHVybiBmaWx0ZXJlZDtcbn1cblxuZXhwb3J0IHsgZ2V0Q2hpbGRLZXksIG9ubHlFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbIkNoaWxkcmVuIiwiaXNWYWxpZEVsZW1lbnQiLCJnZXRDaGlsZEtleSIsImNoaWxkIiwia2V5Iiwib25seUVsZW1lbnRzIiwiY2hpbGRyZW4iLCJmaWx0ZXJlZCIsImZvckVhY2giLCJwdXNoIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/user.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ User)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.522.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\",\n            key: \"975kel\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"17ys0d\"\n        }\n    ]\n];\nconst User = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"user\", __iconNode);\n //# sourceMappingURL=user.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/contact/ContactForm.tsx":
/*!*********************************************************!*\
  !*** ./src/components/features/contact/ContactForm.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactForm: () => (/* binding */ ContactForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Mail,MessageSquare,Send,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* __next_internal_client_entry_do_not_use__ ContactForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ContactForm() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        subject: '',\n        message: '',\n        inquiryType: 'general'\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const inquiryTypes = [\n        {\n            value: 'general',\n            label: 'General Inquiry'\n        },\n        {\n            value: 'collaboration',\n            label: 'Research Collaboration'\n        },\n        {\n            value: 'consulting',\n            label: 'Consulting Opportunity'\n        },\n        {\n            value: 'speaking',\n            label: 'Speaking Engagement'\n        },\n        {\n            value: 'mentoring',\n            label: 'Mentoring Request'\n        },\n        {\n            value: 'other',\n            label: 'Other'\n        }\n    ];\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Name validation\n        if (!formData.name.trim()) {\n            newErrors.name = 'Name is required';\n        } else if (formData.name.trim().length < 2) {\n            newErrors.name = 'Name must be at least 2 characters';\n        }\n        // Email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!formData.email.trim()) {\n            newErrors.email = 'Email is required';\n        } else if (!emailRegex.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        // Subject validation\n        if (!formData.subject.trim()) {\n            newErrors.subject = 'Subject is required';\n        } else if (formData.subject.trim().length < 5) {\n            newErrors.subject = 'Subject must be at least 5 characters';\n        }\n        // Message validation\n        if (!formData.message.trim()) {\n            newErrors.message = 'Message is required';\n        } else if (formData.message.trim().length < 10) {\n            newErrors.message = 'Message must be at least 10 characters';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            // Simulate API call - replace with actual implementation\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // For now, we'll just log the form data\n            console.log('Form submitted:', formData);\n            setSubmitStatus('success');\n            // Reset form after successful submission\n            setFormData({\n                name: '',\n                email: '',\n                subject: '',\n                message: '',\n                inquiryType: 'general'\n            });\n        } catch (error) {\n            console.error('Form submission error:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1,\n                delayChildren: 0.2\n            }\n        }\n    };\n    const formFieldVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20,\n            scale: 0.95\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 300,\n                damping: 24\n            }\n        }\n    };\n    const iconVariants = {\n        hidden: {\n            scale: 0,\n            rotate: -180\n        },\n        visible: {\n            scale: 1,\n            rotate: 0,\n            transition: {\n                type: \"spring\",\n                stiffness: 400,\n                damping: 20,\n                delay: 0.1\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        initial: \"hidden\",\n        animate: \"visible\",\n        variants: containerVariants,\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"max-w-2xl mx-auto backdrop-blur-sm bg-card/95 border-border/50 shadow-xl hover:shadow-2xl transition-all duration-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"space-y-6 pb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: formFieldVariants,\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: iconVariants,\n                                className: \"w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl flex items-center justify-center mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\",\n                                        children: \"Send Me a Message\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        className: \"text-lg mt-3 leading-relaxed\",\n                                        children: \"I'd love to hear from you! Whether you have a question, collaboration idea, or just want to connect, feel free to reach out.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"px-8 pb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                variants: formFieldVariants,\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"space-y-3\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 400,\n                                            damping: 25\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                htmlFor: \"name\",\n                                                className: \"text-sm font-semibold text-foreground/80 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Name *\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"name\",\n                                                        type: \"text\",\n                                                        placeholder: \"Your full name\",\n                                                        value: formData.name,\n                                                        onChange: (e)=>handleInputChange('name', e.target.value),\n                                                        className: \"h-12 px-4 text-base transition-all duration-300 border-2 focus:border-primary/50 hover:border-primary/30 \".concat(errors.name ? 'border-red-500 focus:border-red-500' : 'border-border/50 focus:border-primary', \" \").concat(isSubmitting ? 'opacity-50' : ''),\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                                children: errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: -10,\n                                                        scale: 0.95\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        y: -10,\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                        variant: \"small\",\n                                                        className: \"text-red-500 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            errors.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"space-y-3\",\n                                        whileHover: {\n                                            scale: 1.02\n                                        },\n                                        transition: {\n                                            type: \"spring\",\n                                            stiffness: 400,\n                                            damping: 25\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                htmlFor: \"email\",\n                                                className: \"text-sm font-semibold text-foreground/80 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Email *\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        placeholder: \"<EMAIL>\",\n                                                        value: formData.email,\n                                                        onChange: (e)=>handleInputChange('email', e.target.value),\n                                                        className: \"h-12 px-4 text-base transition-all duration-300 border-2 focus:border-primary/50 hover:border-primary/30 \".concat(errors.email ? 'border-red-500 focus:border-red-500' : 'border-border/50 focus:border-primary', \" \").concat(isSubmitting ? 'opacity-50' : ''),\n                                                        disabled: isSubmitting\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                                children: errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: -10,\n                                                        scale: 0.95\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        y: -10,\n                                                        scale: 0.95\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                        variant: \"small\",\n                                                        className: \"text-red-500 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            errors.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        htmlFor: \"inquiryType\",\n                                        children: \"Type of Inquiry\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"inquiryType\",\n                                        value: formData.inquiryType,\n                                        onChange: (e)=>handleInputChange('inquiryType', e.target.value),\n                                        className: \"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                        disabled: isSubmitting,\n                                        children: inquiryTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: type.value,\n                                                children: type.label\n                                            }, type.value, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        htmlFor: \"subject\",\n                                        children: \"Subject *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"subject\",\n                                        type: \"text\",\n                                        placeholder: \"Brief description of your inquiry\",\n                                        value: formData.subject,\n                                        onChange: (e)=>handleInputChange('subject', e.target.value),\n                                        className: errors.subject ? 'border-red-500' : '',\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.subject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                        variant: \"small\",\n                                        className: \"text-red-500 flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this),\n                                            errors.subject\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        htmlFor: \"message\",\n                                        children: \"Message *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                        id: \"message\",\n                                        placeholder: \"Tell me more about your inquiry, project, or how I can help...\",\n                                        value: formData.message,\n                                        onChange: (e)=>handleInputChange('message', e.target.value),\n                                        className: \"min-h-[120px] \".concat(errors.message ? 'border-red-500' : ''),\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this),\n                                    errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                        variant: \"small\",\n                                        className: \"text-red-500 flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this),\n                                            errors.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                        variant: \"small\",\n                                        className: \"text-muted-foreground\",\n                                        children: [\n                                            formData.message.length,\n                                            \"/500 characters\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        type: \"submit\",\n                                        size: \"lg\",\n                                        className: \"w-full\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Sending Message...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Send Message\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"flex items-center gap-2 text-green-600 bg-green-50 dark:bg-green-900/20 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                variant: \"small\",\n                                                children: \"Thank you! Your message has been sent successfully. I'll get back to you soon.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this),\n                                    submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"flex items-center gap-2 text-red-600 bg-red-50 dark:bg-red-900/20 p-3 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Mail_MessageSquare_Send_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_5__.Typography, {\n                                                variant: \"small\",\n                                                children: \"Sorry, there was an error sending your message. Please try again or contact me directly.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\features\\\\contact\\\\ContactForm.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactForm, \"ZNMba2VJMZnoIQSH2ibEMkVgH+U=\");\n_c = ContactForm;\nvar _c;\n$RefreshReg$(_c, \"ContactForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/contact/ContactForm.tsx\n"));

/***/ })

});