'use client';

import { motion } from 'framer-motion';
import { MapPin, Clock, Heart, Target, Lightbulb, TrendingUp } from 'lucide-react';
import { Typography } from '@/components/ui/Typography';
import { Card, CardContent } from '@/components/ui/Card';
import { JourneyTimeline } from '@/components/features/journey/JourneyTimeline';
import { ANIMATION_VARIANTS } from '@/lib/constants';
// Make sure JourneyStep is exported from '@/types', or update the import path to the correct module
// Update the import path below to the correct location of JourneyStep, for example:
import type { JourneyStep } from '@/types'; // or wherever JourneyStep is actually exported from
// Or, if JourneyStep is defined in a different file, update the path accordingly

interface JourneyPageClientProps {
  journeySteps: JourneyStep[];
}

export function JourneyPageClient({ journeySteps }: JourneyPageClientProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Calculate journey stats
  const journeyStats = {
    totalSteps: journeySteps.length,
    yearsSpanned: new Date().getFullYear() - 2015, // Approximate start
    locationsVisited: new Set(journeySteps.map(step => step.location)).size,
    keyMoments: journeySteps.reduce((total, step) => total + step.keyMoments.length, 0),
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          From Mud Walls to World Halls: My Journey Across Borders
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          &quot;An interactive journey of growth, resilience, and discovery — from humble beginnings 
          in a small village in Nepal to navigating the vibrant world of technology, research, and innovation. Each challenge overcome, 
          and every milestone reached, has helped shape the person I am today.&quot;
        </Typography>
      </motion.div>

      {/* Journey Statistics */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
      >
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Target className="h-8 w-8 text-primary" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {journeyStats.totalSteps}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Major Milestones
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <Clock className="h-8 w-8 text-accent" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {journeyStats.yearsSpanned}+
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Years of Growth
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <MapPin className="h-8 w-8 text-primary" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {journeyStats.locationsVisited}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Different Locations
          </Typography>
        </div>
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <TrendingUp className="h-8 w-8 text-accent" />
          </div>
          <Typography variant="h3" className="text-2xl font-bold text-primary">
            {journeyStats.keyMoments}
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            Key Moments
          </Typography>
        </div>
      </motion.div>

      {/* Interactive Journey Timeline */}
      <JourneyTimeline journeySteps={journeySteps} />

      {/* Journey Insights */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={containerVariants}
        className="space-y-8"
      >
        <Typography variant="h2" className="text-2xl font-bold text-center">
          Lessons from the Journey
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <motion.div variants={ANIMATION_VARIANTS.slideUp}>
            <Card className="h-full text-center p-6">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto">
                  <Heart className="h-8 w-8 text-red-600" />
                </div>
                <Typography variant="h4" className="font-semibold">
                  Resilience & Determination
                </Typography>
                <Typography variant="p" className="text-muted-foreground text-sm">
                  Every challenge faced, from No resources in the village to competitive 
                  job markets in the city, taught me that persistence and adaptability are 
                  key to overcoming obstacles.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={ANIMATION_VARIANTS.slideUp}>
            <Card className="h-full text-center p-6">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto">
                  <Lightbulb className="h-8 w-8 text-blue-600" />
                </div>
                <Typography variant="h4" className="font-semibold">
                  Continuous Learning
                </Typography>
                <Typography variant="p" className="text-muted-foreground text-sm">
                  From learning ABCD with borrowed books to pursuing a Mphil Degree, Now a PHD Aspirant, 
                  the journey reinforced that learning never stops. Each phase brought 
                  new knowledge and perspectives.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={ANIMATION_VARIANTS.slideUp}>
            <Card className="h-full text-center p-6">
              <CardContent className="space-y-4">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
                  <Target className="h-8 w-8 text-green-600" />
                </div>
                <Typography variant="h4" className="font-semibold">
                  Giving Back
                </Typography>
                <Typography variant="p" className="text-muted-foreground text-sm">
                  Success is meaningful when shared. Throughout the journey, volunteering 
                  and mentoring others has been as important as personal achievements, 
                  creating a cycle of positive impact.
                </Typography>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>

      {/* Personal Reflection */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center"
      >
        <Card className="max-w-4xl mx-auto bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20">
          <CardContent className="p-8 space-y-6">
            <Typography variant="h3" className="text-2xl font-bold">
              Reflections on the Journey
            </Typography>
            <Typography variant="p" className="text-muted-foreground leading-relaxed">
              Looking back at this journey, I'm struck by how each phase prepared me for the next. 
              The resourcefulness learned in the village helped me navigate limited resources as a student. 
              The community values instilled early on drive my commitment to volunteering and mentoring. 
              The technical skills gained in industry inform my research approach in academia.
            </Typography>
            <Typography variant="p" className="text-muted-foreground leading-relaxed">
              This journey from village to city to world isn't just about geographical movement—it's about 
              expanding horizons while staying grounded in core values. It's about embracing 
              opportunities while remembering where you came from. Most importantly, it's about 
              using your growth to lift others up along the way.
            </Typography>
            <Typography variant="p" className="text-muted-foreground leading-relaxed">
              The journey continues, and I&#39;m excited about the chapters yet to be written. 
              Whether in research labs, community centers, or mentoring sessions, the goal 
              remains the same: to learn, grow, and make a positive impact on the world around us.
            </Typography>
          </CardContent>
        </Card>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8"
      >
        <div className="max-w-2xl mx-auto p-6 bg-muted/50 rounded-lg border">
          <Typography variant="h4" className="mb-4">
            Inspired by This Journey?
          </Typography>
          <Typography variant="p" className="text-muted-foreground mb-6">
            Everyone&#39;s journey is unique, but we can learn from each other&#39;s experiences. 
            If you&#39;d like to share your story, discuss career transitions, or explore 
            collaboration opportunities, I&#39;d love to connect.
          </Typography>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Typography variant="link" href="/contact" className="inline-block">
                Let&#39;s Connect
              </Typography>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Typography variant="link" href="/about" className="inline-block">
                Learn More About Me
              </Typography>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
