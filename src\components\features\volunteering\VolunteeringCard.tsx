'use client';

import { motion } from 'framer-motion';
import { Calendar, MapPin, Users, TrendingUp, Heart } from 'lucide-react';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Typography } from '@/components/ui/Typography';
import { ANIMATION_VARIANTS } from '@/lib/constants';
import type { VolunteeringExperience } from '@/types';

interface VolunteeringCardProps {
  experience: VolunteeringExperience;
  index?: number;
}

export function VolunteeringCard({ experience, index = 0 }: VolunteeringCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  };

  const getVolunteeringDuration = () => {
    const start = new Date(experience.startDate);
    const end = experience.endDate ? new Date(experience.endDate) : new Date();
    const years = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365));
    const months = Math.floor(((end.getTime() - start.getTime()) % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24 * 30));
    
    if (years > 0) {
      return months > 0 ? `${years} year${years > 1 ? 's' : ''}, ${months} month${months > 1 ? 's' : ''}` : `${years} year${years > 1 ? 's' : ''}`;
    }
    return `${months} month${months > 1 ? 's' : ''}`;
  };

  return (
    <motion.div
      variants={ANIMATION_VARIANTS.slideUp}
      whileHover={{ y: -8 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="h-full hover:shadow-xl transition-all duration-300 group overflow-hidden">
        {/* Organization Image */}
        {experience.imageUrl && (
          <div className="relative h-48 overflow-hidden">
            <Image
              src={experience.imageUrl}
              alt={`${experience.organization} volunteering`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
            
            {/* Current Badge */}
            {experience.current && (
              <div className="absolute top-4 right-4">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                  <Heart className="w-3 h-3 mr-1" />
                  Current
                </span>
              </div>
            )}

            {/* Organization Name Overlay */}
            <div className="absolute bottom-4 left-4 right-4">
              <Typography variant="h4" className="text-white font-bold text-lg">
                {experience.organization}
              </Typography>
            </div>
          </div>
        )}

        <CardHeader className="space-y-4">
          {/* Role and Duration */}
          <div className="space-y-2">
            <CardTitle className="text-xl group-hover:text-primary transition-colors leading-tight">
              {experience.role}
            </CardTitle>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4" />
                <span>
                  {formatDate(experience.startDate)} - {experience.endDate ? formatDate(experience.endDate) : 'Present'}
                </span>
              </div>
              <span className="text-xs">({getVolunteeringDuration()})</span>
            </div>
            {experience.location && (
              <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>{experience.location}</span>
              </div>
            )}
          </div>

          {/* Description */}
          <CardDescription className="text-sm line-clamp-3">
            {experience.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Impact Statement */}
          {experience.impact && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-accent" />
                <Typography variant="small" className="font-medium">Impact</Typography>
              </div>
              <Typography variant="p" className="text-sm text-muted-foreground bg-accent/10 p-3 rounded-lg">
                {experience.impact}
              </Typography>
            </div>
          )}

          {/* Skills Developed */}
          {experience.skills && experience.skills.length > 0 && (
            <div className="space-y-2">
              <Typography variant="small" className="font-medium">Skills Developed</Typography>
              <div className="flex flex-wrap gap-1">
                {experience.skills.map((skill) => (
                  <span
                    key={skill}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-muted text-muted-foreground hover:bg-primary hover:text-primary-foreground transition-colors"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Key Highlights */}
          {experience.highlights && experience.highlights.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-primary" />
                <Typography variant="small" className="font-medium">Key Achievements</Typography>
              </div>
              <ul className="space-y-1">
                {experience.highlights.slice(0, 3).map((highlight, idx) => (
                  <li key={idx} className="text-xs text-muted-foreground flex items-start">
                    <span className="text-primary mr-2">•</span>
                    <span>{highlight}</span>
                  </li>
                ))}
                {experience.highlights.length > 3 && (
                  <li className="text-xs text-muted-foreground flex items-start">
                    <span className="text-primary mr-2">•</span>
                    <span>+{experience.highlights.length - 3} more achievements</span>
                  </li>
                )}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
